@echo off
echo Building Smart Edu for Windows (Debug)...

REM Clean previous builds
echo Cleaning previous builds...
flutter clean

REM Get dependencies
echo Getting dependencies...
flutter pub get

REM Set environment variables for better Firebase build
set FIREBASE_CPP_SDK_DIR=%CD%\build\windows\x64\extracted\firebase_cpp_sdk_windows
set CMAKE_BUILD_PARALLEL_LEVEL=2

REM Build for Windows in debug mode with optimized settings
echo Building Windows application (Debug)...
flutter build windows --debug --dart-define=FLAVOR=student

if %ERRORLEVEL% EQU 0 (
    echo Build completed successfully!
    echo Executable location: build\windows\x64\runner\Debug\smart_test.exe
) else (
    echo Build failed with error code %ERRORLEVEL%
    echo Please check the error messages above.
)

pause
