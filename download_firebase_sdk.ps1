# PowerShell script to download Firebase C++ SDK for Windows
$sdkVersion = "12.8.0"
$zipFileName = "firebase_cpp_sdk_windows_$sdkVersion.zip"
$extractPath = "build\windows\x64\extracted"
$zipPath = "build\windows\x64\$zipFileName"

Write-Host "=== Firebase C++ SDK Download Script ==="
Write-Host "SDK Version: $sdkVersion"
Write-Host "Expected file size: ~60-80 MB"
Write-Host "========================================="

# Create directories if they don't exist
New-Item -ItemType Directory -Force -Path "build\windows\x64" | Out-Null
New-Item -ItemType Directory -Force -Path $extractPath | Out-Null

# Download the SDK
try {
    $directUrl = "https://dl.google.com/firebase/sdk/cpp/firebase_cpp_sdk_$sdkVersion.zip"
    Write-Host "Downloading from: $directUrl"
    Write-Host "Starting download... This may take 10-30 minutes depending on your internet speed."
    Write-Host "Please be patient and do not interrupt the download."
    Write-Host ""

    # Download with progress
    $progressPreference = 'Continue'
    Invoke-WebRequest -Uri $directUrl -OutFile $zipPath -UseBasicParsing

    # Check if download was successful
    if (Test-Path $zipPath) {
        $fileSize = (Get-Item $zipPath).Length / 1MB
        Write-Host ""
        Write-Host "Download completed successfully!"
        Write-Host "File size: $([math]::Round($fileSize, 2)) MB"

        # Verify file is not corrupted
        if ($fileSize -lt 10) {
            $sizeText = [math]::Round($fileSize, 2)
            throw "Downloaded file is too small ($sizeText MB). Download may have failed."
        }

        Write-Host ""
        Write-Host "Extracting Firebase SDK..."
        Expand-Archive -Path $zipPath -DestinationPath $extractPath -Force

        # Rename the extracted folder
        $extractedFolder = Get-ChildItem -Path $extractPath -Directory | Where-Object { $_.Name -like "firebase_cpp_sdk*" } | Select-Object -First 1
        if ($extractedFolder) {
            $targetPath = Join-Path $extractPath "firebase_cpp_sdk_windows"
            if (Test-Path $targetPath) {
                Remove-Item $targetPath -Recurse -Force
            }
            Rename-Item $extractedFolder.FullName $targetPath
            Write-Host "Firebase SDK extracted and renamed successfully!"

            # Verify extraction
            $libPath = Join-Path $targetPath "libs\windows"
            if (Test-Path $libPath) {
                Write-Host "Firebase SDK libraries found at: $libPath"

                # Count library files
                $libFiles = Get-ChildItem -Path $libPath -Recurse -File | Measure-Object
                Write-Host "Found $($libFiles.Count) library files"
            } else {
                Write-Host "Warning: Firebase SDK libraries not found in expected location"
            }
        } else {
            Write-Host "Warning: Could not find extracted Firebase SDK folder"
        }

        # Clean up ZIP file
        Remove-Item $zipPath -Force
        Write-Host "Cleaned up ZIP file"

        Write-Host ""
        Write-Host "========================================="
        Write-Host "Firebase SDK setup completed successfully!"
        Write-Host "You can now continue with the Flutter build process."
        Write-Host "========================================="

    } else {
        throw "Download failed - ZIP file not found"
    }

} catch {
    Write-Host ""
    Write-Host "Error downloading Firebase SDK: $_"
    Write-Host ""
    Write-Host "Troubleshooting tips:"
    Write-Host "1. Check your internet connection"
    Write-Host "2. Try running the script again"
    Write-Host "3. Make sure you have enough disk space (at least 500MB free)"
    Write-Host "4. Check if antivirus is blocking the download"
    exit 1
}
