# PowerShell script to download and setup NuGet.exe
Write-Host "=== NuGet Download and Setup Script ==="

# Create NuGet directory
$nugetPath = "C:\NuGet"
if (!(Test-Path $nugetPath)) {
    New-Item -ItemType Directory -Path $nugetPath -Force | Out-Null
    Write-Host "Created NuGet directory at C:\NuGet"
}

# Download NuGet.exe
$nugetExePath = Join-Path $nugetPath "nuget.exe"
Write-Host "Downloading NuGet.exe..."

try {
    Invoke-WebRequest -Uri "https://dist.nuget.org/win-x86-commandline/latest/nuget.exe" -OutFile $nugetExePath -UseBasicParsing
    
    if (Test-Path $nugetExePath) {
        Write-Host "NuGet.exe downloaded successfully to: $nugetExePath"
        
        # Add to PATH
        $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
        if ($currentPath -notlike "*C:\NuGet*") {
            $newPath = $currentPath + ";C:\NuGet"
            [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
            Write-Host "Added C:\NuGet to system PATH"
        } else {
            Write-Host "C:\NuGet already exists in system PATH"
        }
        
        # Update PATH for current session
        $env:PATH += ";C:\NuGet"
        
        # Test NuGet
        Write-Host "Testing NuGet installation..."
        & $nugetExePath help | Select-Object -First 3
        Write-Host ""
        Write-Host "========================================="
        Write-Host "NuGet setup completed successfully!"
        Write-Host "You may need to restart your terminal for PATH changes to take effect."
        Write-Host "========================================="
        
    } else {
        throw "NuGet.exe file not found after download"
    }
    
} catch {
    Write-Host "Error downloading NuGet: $_"
    Write-Host ""
    Write-Host "Manual download instructions:"
    Write-Host "1. Go to: https://www.nuget.org/downloads"
    Write-Host "2. Download nuget.exe"
    Write-Host "3. Place it in C:\NuGet\nuget.exe"
    Write-Host "4. Add C:\NuGet to your system PATH"
    exit 1
}
