rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // دالة للتحقق من أن المستخدم هو إدارة
    // Function to check if user is admin
    function isAdmin() {
      return request.auth != null &&
             request.auth.token.firebase.identities['firebase'] != null;
    }

    // قواعد للمجموعات العامة
    // Public collections rules
    match /subjects/{document} {
      allow read: if true;
      allow write: if request.auth != null; // السماح للمستخدمين المسجلين بالكتابة
    }

    match /sections/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    match /exams/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    match /units/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    match /lessons/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    match /questions/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    match /courses/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    match /videos/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // قواعد للإعدادات العامة
    // Public settings
    match /settings/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    match /pricing_messages/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // قواعد لأقسام الفيديوهات
    // Video sections rules
    match /video_sections/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // قواعد لأقسام الاختبارات
    // Test sections rules
    match /test_sections/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // قواعد للرسائل الإدارية
    // Admin messages rules
    match /admin_messages/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // قواعد للإعلانات
    // Announcements rules
    match /announcements/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // قواعد لبيانات المستخدمين (للمستخدمين المسجلين فقط)
    // User data rules (for authenticated users only)
    match /user_statistics/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_favorites/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_wrong_answers/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_subscriptions/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_notes/{deviceId} {
      allow read, write: if request.auth != null;
    }

    match /user_video_progress/{deviceId} {
      allow read, write: if request.auth != null;
    }

    // قواعد للأكواد (قراءة وكتابة للمستخدمين المسجلين)
    // Codes rules (read and write for authenticated users)
    match /subscription_codes/{document} {
      allow read: if request.auth != null; // للتحقق من صحة الكود
      allow write: if request.auth != null; // لتحديث حالة الكود عند الاستخدام
    }

    match /activation_codes/{document} {
      allow read: if request.auth != null; // للتحقق من صحة الكود
      allow write: if request.auth != null; // لتحديث حالة الكود عند الاستخدام
    }

    // قواعد للمجموعات الفرعية في الوثائق
    // Rules for subcollections
    match /{path=**}/questions/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    match /{path=**}/videos/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // قواعد عامة للمجموعات الأخرى
    // General rules for other collections
    match /app_config/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    match /version_info/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // منع الوصول لأي مجموعات أخرى
    // Deny access to any other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
