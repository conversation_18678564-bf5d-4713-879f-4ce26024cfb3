import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:barcode_widget/barcode_widget.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subscription_code_model.dart';

class GeneratedCodesDisplayWidget extends StatefulWidget {
  final List<SubscriptionCode> codes;
  final VoidCallback? onClose;

  const GeneratedCodesDisplayWidget({
    super.key,
    required this.codes,
    this.onClose,
  });

  @override
  State<GeneratedCodesDisplayWidget> createState() =>
      _GeneratedCodesDisplayWidgetState();
}

class _GeneratedCodesDisplayWidgetState
    extends State<GeneratedCodesDisplayWidget> {
  int _currentIndex = 0;
  PageController _pageController = PageController();

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.codes.isEmpty) {
      return const SizedBox.shrink();
    }

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'الأكواد المُنشأة (${_currentIndex + 1}/${widget.codes.length})',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.successGradient),
        ),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () => _copyAllCodes(),
            icon: const Icon(Icons.copy_all),
            tooltip: 'نسخ جميع الأكواد',
          ),
          IconButton(
            onPressed: widget.onClose ?? () => Navigator.pop(context),
            icon: const Icon(Icons.close),
            tooltip: 'إغلاق',
          ),
        ],
      ),
      body: Column(
        children: [
          // مؤشر التقدم
          if (widget.codes.length > 1) _buildProgressIndicator(),

          // عرض الكود الحالي
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              itemCount: widget.codes.length,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemBuilder: (context, index) {
                return _buildCodeCard(widget.codes[index]);
              },
            ),
          ),

          // أزرار التنقل
          if (widget.codes.length > 1) _buildNavigationButtons(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: LinearProgressIndicator(
        value: (_currentIndex + 1) / widget.codes.length,
        backgroundColor: AppTheme.dividerColor,
        valueColor: AlwaysStoppedAnimation<Color>(AppTheme.successColor),
      ),
    );
  }

  Widget _buildCodeCard(SubscriptionCode code) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Card(
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Padding(
          padding: EdgeInsets.all(24.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // عنوان الكود
              Text(
                'كود التفعيل',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),

              SizedBox(height: 24.h),

              // الكود النصي
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(
                    color: AppTheme.primaryColor.withValues(alpha: 0.3),
                    width: 2,
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      code.code,
                      style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'monospace',
                        color: AppTheme.primaryColor,
                        letterSpacing: 2.0,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 8.h),
                    ElevatedButton.icon(
                      onPressed: () => _copyCode(code.code),
                      icon: Icon(Icons.copy, size: 16.sp),
                      label: const Text('نسخ الكود'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 8.h,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: 32.h),

              // QR Code
              Text(
                'رمز الاستجابة السريعة (QR)',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
              ),

              SizedBox(height: 16.h),

              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // QR Code مربع
                    QrImageView(
                      data: code.code,
                      version: QrVersions.auto,
                      size: 200.w,
                      backgroundColor: Colors.white,
                      eyeStyle: const QrEyeStyle(
                        eyeShape: QrEyeShape.square,
                        color: Colors.black,
                      ),
                      dataModuleStyle: const QrDataModuleStyle(
                        dataModuleShape: QrDataModuleShape.square,
                        color: Colors.black,
                      ),
                    ),
                    SizedBox(height: 12.h),
                    // النص تحت الباركود
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Text(
                        code.code,
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'monospace',
                          color: Colors.black87,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: 32.h),

              // Barcode
              Text(
                'الباركود',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
              ),

              SizedBox(height: 16.h),

              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: BarcodeWidget(
                  barcode: Barcode.code128(),
                  data: code.code,
                  width: 300.w,
                  height: 80.h,
                  style: TextStyle(fontSize: 12.sp, color: Colors.black),
                ),
              ),

              SizedBox(height: 24.h),

              // معلومات الكود
              _buildCodeInfo(code),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCodeInfo(SubscriptionCode code) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppTheme.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الكود',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 12.h),
          _buildInfoRow('الحالة', _getStatusText(code.status)),
          _buildInfoRow('تاريخ الإنشاء', _formatDate(code.createdAt)),
          if (code.expiresAt != null)
            _buildInfoRow('تاريخ الانتهاء', _formatDate(code.expiresAt!)),
          if (code.notes != null && code.notes!.isNotEmpty)
            _buildInfoRow('الملاحظات', code.notes!),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.w,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: AppTheme.textSecondaryColor,
                fontSize: 14.sp,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: AppTheme.textPrimaryColor,
                fontSize: 14.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ElevatedButton.icon(
            onPressed: _currentIndex > 0 ? _previousCode : null,
            icon: const Icon(Icons.arrow_back_ios),
            label: const Text('السابق'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.textSecondaryColor,
              foregroundColor: Colors.white,
            ),
          ),
          Text(
            '${_currentIndex + 1} من ${widget.codes.length}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          ElevatedButton.icon(
            onPressed: _currentIndex < widget.codes.length - 1
                ? _nextCode
                : null,
            icon: const Icon(Icons.arrow_forward_ios),
            label: const Text('التالي'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _previousCode() {
    if (_currentIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextCode() {
    if (_currentIndex < widget.codes.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _copyCode(String code) {
    Clipboard.setData(ClipboardData(text: code));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم نسخ الكود: $code'),
        backgroundColor: AppTheme.successColor,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _copyAllCodes() {
    final allCodes = widget.codes.map((code) => code.code).join('\n');
    Clipboard.setData(ClipboardData(text: allCodes));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم نسخ جميع الأكواد (${widget.codes.length} كود)'),
        backgroundColor: AppTheme.successColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  String _getStatusText(CodeStatus status) {
    switch (status) {
      case CodeStatus.active:
        return 'نشط';
      case CodeStatus.used:
        return 'مستخدم';
      case CodeStatus.expired:
        return 'منتهي الصلاحية';
      default:
        return 'غير معروف';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
