import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/adaptive_sizing.dart';
import '../../../../core/widgets/adaptive_widgets.dart';
import '../../../../core/models/section.dart';
import '../../../../shared/services/section_service.dart';
import '../../../../shared/services/exam_service.dart';
import '../../../../shared/services/content_service.dart';
import '../../../../shared/services/subscription_service.dart';

import 'subjects_by_section_page.dart';
import 'free_sections_page.dart';
import 'questions_viewer_page.dart';
import 'subject_units_page.dart';

class SectionsPage extends StatefulWidget {
  const SectionsPage({super.key});

  @override
  State<SectionsPage> createState() => _SectionsPageState();
}

class _SectionsPageState extends State<SectionsPage> {
  List<Section> _sections = [];
  bool _isLoading = true; // تحميل البيانات المحفوظة
  bool _isNetworkLoading = false; // تحميل من الشبكة
  bool _hasTriedLoading = false; // هل تم محاولة التحميل

  @override
  void initState() {
    super.initState();
    debugPrint('🔥 بدء تحميل صفحة الأقسام...');
    _loadSections();
  }

  Future<void> _loadSections() async {
    try {
      // الحصول على الأقسام المحفوظة مباشرة
      final savedSections = await SectionService.instance.getPaidSections();

      if (savedSections.isNotEmpty) {
        setState(() {
          _sections = savedSections;
          _isLoading = false;
          _hasTriedLoading = true;
        });
        debugPrint('🚀 تم عرض ${_sections.length} قسم حقيقي مباشرة من الكاش!');
      } else {
        setState(() {
          _isLoading = false;
          _hasTriedLoading = true;
        });
        debugPrint('📱 لا توجد أقسام محفوظة - سيتم التحميل من الشبكة');
      }

      // تحديث البيانات في الخلفية بصمت (سواء كانت موجودة أم لا)
      _updateFromNetworkSilently();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الأقسام: $e');
      setState(() {
        _isLoading = false;
        _hasTriedLoading = true;
      });
    }
  }

  /// تحديث البيانات في الخلفية بدون تأثير على العرض
  Future<void> _updateFromNetworkSilently() async {
    if (_isNetworkLoading) return; // تجنب التحديث المتعدد

    try {
      setState(() {
        _isNetworkLoading = true;
      });

      debugPrint('🔄 تحديث الأقسام في الخلفية...');

      // محاولة التحديث من Firebase
      final success = await SectionService.instance
          .updateSectionsFromFirebase();

      if (success && mounted) {
        final updatedSections = await SectionService.instance.getPaidSections();
        if (updatedSections.isNotEmpty && mounted) {
          setState(() {
            _sections = updatedSections;
            _isNetworkLoading = false;
          });
          debugPrint(
            '✅ تم تحديث ${_sections.length} قسم من Firebase في الخلفية',
          );
        } else {
          setState(() {
            _isNetworkLoading = false;
          });
        }
      } else {
        setState(() {
          _isNetworkLoading = false;
        });
        debugPrint('⚠️ فشل في تحديث الأقسام من Firebase');
      }
    } catch (e) {
      setState(() {
        _isNetworkLoading = false;
      });
      debugPrint('❌ خطأ في التحديث في الخلفية: $e');
    }
  }

  /// تحديث يدوي للأقسام (Pull-to-Refresh)
  Future<void> _refreshSections() async {
    try {
      debugPrint('🔄 تحديث يدوي للأقسام...');
      await _updateFromNetworkSilently();
    } catch (e) {
      debugPrint('❌ خطأ في التحديث اليدوي للأقسام: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحديث الأقسام: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AdaptiveAppBar(
        title: 'الاختبارات',
        gradient: AppTheme.primaryGradient,
        leading: IconButton(
          icon: AdaptiveIcon(Icons.search, size: 24, color: Colors.white),
          onPressed: _showQuestionSearchDialog,
          tooltip: 'البحث عن سؤال',
        ),
        actions: [
          if (_isNetworkLoading)
            Padding(
              padding: EdgeInsets.only(left: 8.adaptiveSpacing),
              child: SizedBox(
                width: 16.adaptiveIcon,
                height: 16.adaptiveIcon,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
          // زر تجربة التطبيق مع شعار الاختبارات
          Container(
            margin: EdgeInsets.only(left: 8.adaptiveSpacing),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(20.adaptiveRadius),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const FreeSectionsPage(),
                    ),
                  );
                },
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.adaptiveSpacing,
                    vertical: 6.adaptiveSpacing,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20.adaptiveRadius),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      AdaptiveIcon(
                        Icons.quiz_outlined,
                        size: 20,
                        color: Colors.white,
                      ),
                      SizedBox(width: 4.adaptiveSpacing),
                      AdaptiveText(
                        'تجربة التطبيق',
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    // إذا كان هناك بيانات، نعرضها فوراً
    if (_sections.isNotEmpty) {
      return RefreshIndicator(
        onRefresh: _refreshSections,
        child: _buildSectionsContent(),
      );
    }

    // إذا كان يحمل من الشبكة، نعرض مؤشر التحميل
    if (_isNetworkLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل الأقسام...'),
          ],
        ),
      );
    }

    // إذا كان التحميل الأولي، نعرض مؤشر التحميل
    if (_isLoading || !_hasTriedLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    // فقط إذا حاولنا التحميل ولم توجد بيانات، نعرض رسالة فارغة
    return _buildEmptyState();
  }

  Widget _buildSectionsContent() {
    return Padding(
      padding: EdgeInsets.all(16.adaptiveSpacing),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          SizedBox(height: 20.adaptiveSpacing),
          Expanded(
            child: AdaptiveGridView(
              shrinkWrap: false,
              children: _sections
                  .map((section) => _buildSectionCard(section))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.adaptiveSpacing),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(16.adaptiveRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.adaptiveSpacing),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.adaptiveRadius),
            ),
            child: AdaptiveIcon(Icons.school, size: 24, color: Colors.white),
          ),
          SizedBox(width: 16.adaptiveSpacing),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AdaptiveText(
                  'مكتبة الاختبارات',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                SizedBox(height: 4.adaptiveSpacing),
                AdaptiveText(
                  'اختر القسم لحل الاختبارات التعليمية',
                  fontSize: 14,
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(Section section) {
    return AdaptiveSectionCard(
      title: section.name,
      subtitle: section.description,
      backgroundColor: _getSectionColor(section.color),
      onTap: () => _navigateToSubjects(section),
      leading: AdaptiveIcon(Icons.school, size: 32, color: Colors.white),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AdaptiveIcon(
            Icons.school_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.adaptiveSpacing),
          AdaptiveText(
            'لا توجد أقسام متاحة',
            fontSize: 18,
            color: Colors.grey[600],
            fontWeight: FontWeight.w600,
          ),
          SizedBox(height: 8.adaptiveSpacing),
          AdaptiveText(
            'تأكد من الاتصال بالإنترنت وحاول مرة أخرى',
            fontSize: 14,
            color: Colors.grey[500],
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.adaptiveSpacing),
          AdaptiveElevatedButton(
            onPressed: _isNetworkLoading ? null : _updateFromNetworkSilently,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                AdaptiveIcon(Icons.refresh, size: 20, color: Colors.white),
                SizedBox(width: 8.adaptiveSpacing),
                AdaptiveText(
                  'إعادة المحاولة',
                  fontSize: 14,
                  color: Colors.white,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToSubjects(Section section) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SubjectsBySectionPage(section: section),
      ),
    );
  }

  Color _getSectionColor(String colorString) {
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return AppTheme.primaryColor; // لون افتراضي في حالة الخطأ
    }
  }

  /// عرض نافذة البحث عن السؤال
  void _showQuestionSearchDialog() {
    final TextEditingController controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.search, color: AppTheme.primaryColor),
            SizedBox(width: 8.w),
            Text('البحث عن سؤال'),
          ],
        ),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            labelText: 'رقم السؤال',
            hintText: 'مثال: #bi5WY8W7MwLX2U2QzwAl',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.tag),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final input = controller.text.trim();
              if (input.isNotEmpty) {
                Navigator.pop(context);
                // إزالة الهاشتاغ إذا كان موجوداً
                final questionId = input.startsWith('#')
                    ? input.substring(1).trim()
                    : input;
                _searchForQuestion(questionId);
              }
            },
            child: Text('بحث'),
          ),
        ],
      ),
    );
  }

  /// البحث عن السؤال بالرقم مع التحقق من الاشتراك
  Future<void> _searchForQuestion(String questionId) async {
    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(20.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16.h),
                  Text('جاري البحث عن السؤال...'),
                ],
              ),
            ),
          ),
        ),
      );

      // البحث عن السؤال
      final question = await ExamService.instance.getQuestionById(questionId);

      // إغلاق مؤشر التحميل
      if (mounted) Navigator.pop(context);

      if (question != null) {
        // التحقق من الاشتراك في المادة
        final isSubscribed = SubscriptionService.instance.isSubscribedToSubject(
          question.subjectId,
        );

        if (isSubscribed) {
          // الحصول على بيانات المادة
          final subject = await ContentService.instance.getSubjectById(
            question.subjectId,
          );

          if (subject != null && mounted) {
            // الانتقال إلى السؤال
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => QuestionsViewerPage(
                  title: 'السؤال المطلوب',
                  subject: subject,
                  questionType: QuestionFilterType.single,
                  singleQuestionId: question.id,
                ),
              ),
            );
          } else if (mounted) {
            _showErrorDialog('لم يتم العثور على بيانات المادة');
          }
        } else if (mounted) {
          // عرض رسالة تطلب الاشتراك
          _showSubscriptionRequiredDialog(question.subjectId);
        }
      } else if (mounted) {
        _showErrorDialog('لم يتم العثور على سؤال بهذا الرقم');
      }
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحاً
      if (mounted) {
        Navigator.pop(context);
        _showErrorDialog('حدث خطأ أثناء البحث: ${e.toString()}');
      }
    }
  }

  /// عرض رسالة خطأ
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8.w),
            Text('خطأ'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// عرض رسالة تطلب الاشتراك
  void _showSubscriptionRequiredDialog(String subjectId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.lock, color: Colors.orange),
            SizedBox(width: 8.w),
            Text('اشتراك مطلوب'),
          ],
        ),
        content: Text(
          'السؤال من مادة غير مشترك بها. يرجى تفعيل اشتراك للوصول إليه.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('موافق'),
          ),
        ],
      ),
    );
  }
}
