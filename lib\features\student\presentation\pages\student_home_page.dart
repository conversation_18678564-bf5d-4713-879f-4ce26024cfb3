import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/adaptive_sizing.dart';
import '../../../../core/utils/logger.dart';
import '../../../../shared/services/subscription_service.dart';
import 'subscription_activation_page.dart';
import 'subject_detail_page.dart';
import 'sections_page.dart';
import 'video_sections_page.dart';
import '../../../../shared/models/subject_model.dart';

class StudentHomePage extends StatefulWidget {
  const StudentHomePage({super.key});

  @override
  State<StudentHomePage> createState() => _StudentHomePageState();
}

class _StudentHomePageState extends State<StudentHomePage> {
  int _currentIndex = 2; // البدء من صفحة الفيديوهات

  final List<Widget> _pages = [
    const SubscriptionActivationPage(), // تفعيل الاشتراك
    const SectionsPage(), // الاختبارات (الأقسام)
    const VideoSectionsPage(), // الفيديوهات
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Smart Edu',
          style: Theme.of(
            context,
          ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
        ),
        foregroundColor: Colors.white,
      ),
      body: _pages[_currentIndex],
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.white,
          selectedItemColor: AppTheme.primaryColor,
          unselectedItemColor: AppTheme.textSecondaryColor,
          selectedLabelStyle: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.normal,
          ),
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.card_membership_outlined),
              activeIcon: Icon(Icons.card_membership),
              label: 'تفعيل الاشتراك',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.quiz_outlined),
              activeIcon: Icon(Icons.quiz),
              label: 'الاختبارات',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.video_library_outlined),
              activeIcon: Icon(Icons.video_library),
              label: 'الفيديوهات',
            ),
          ],
        ),
      ),
    );
  }
}

class SubjectsPage extends StatefulWidget {
  const SubjectsPage({super.key});

  @override
  State<SubjectsPage> createState() => _SubjectsPageState();
}

class _SubjectsPageState extends State<SubjectsPage> {
  List<Subject> _subjects = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSubjects();

    // الاستماع لتغييرات SubscriptionService
    SubscriptionService.instance.addListener(_onSubscriptionChanged);
  }

  @override
  void dispose() {
    SubscriptionService.instance.removeListener(_onSubscriptionChanged);
    super.dispose();
  }

  /// معالج تغييرات الاشتراك
  void _onSubscriptionChanged() {
    if (mounted) {
      setState(() {
        // عرض المواد المدفوعة فقط في الصفحة الرئيسية
        _subjects = SubscriptionService.instance.paidSubjects;
      });
    }
  }

  Future<void> _loadSubjects() async {
    try {
      // استخدام SubscriptionService للحصول على المواد
      final subscriptionService = SubscriptionService.instance;

      // إذا لم تكن الخدمة مهيأة، قم بتهيئتها
      if (subscriptionService.allSubjects.isEmpty) {
        await subscriptionService.initialize();
      }

      if (mounted) {
        setState(() {
          // عرض المواد المدفوعة فقط في الصفحة الرئيسية
          _subjects = subscriptionService.paidSubjects;
          _isLoading = false;
        });
      }
    } catch (e) {
      Logger.error('Error loading subjects', e);
      // في حالة فشل تحميل البيانات، إرجاع قائمة فارغة
      final mockSubjects = <Subject>[];

      if (mounted) {
        setState(() {
          _subjects = mockSubjects;
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_subjects.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.book_outlined,
              size: 80,
              color: AppTheme.textSecondaryColor,
            ),
            SizedBox(height: 16.h),
            Text(
              'لا توجد مواد متاحة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'سيتم إضافة المواد قريباً',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }
    return RefreshIndicator(
      onRefresh: _loadSubjects,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المواد الدراسية',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    setState(() {
                      _isLoading = true;
                    });
                    _loadSubjects();
                  },
                  icon: Icon(Icons.refresh, color: AppTheme.primaryColor),
                  tooltip: 'تحديث',
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Expanded(
              child: Consumer<SubscriptionService>(
                builder: (context, subscriptionService, child) {
                  return GridView.builder(
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: AdaptiveSizing.instance
                          .adaptiveGridColumns(
                            mobileColumns: 2,
                            tabletColumns: 3,
                            desktopColumns: 4, // 4 أعمدة على Windows
                          ),
                      crossAxisSpacing: 16.w,
                      mainAxisSpacing: 16.h,
                      childAspectRatio: AdaptiveSizing.instance
                          .adaptiveCardAspectRatio(
                            mobileRatio: 0.8,
                            tabletRatio: 0.85,
                            desktopRatio: 0.95, // نسبة أفضل للشاشات الكبيرة
                          ),
                    ),
                    itemCount: _subjects.length,
                    itemBuilder: (context, index) {
                      return _buildSubjectCard(context, _subjects[index]);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubjectCard(BuildContext context, Subject subject) {
    // التحقق من الاشتراك في المادة
    final isSubscribed = SubscriptionService.instance.isSubscribedToSubject(
      subject.id,
    );

    // تحويل اللون من hex string إلى Color
    Color subjectColor = AppTheme.primaryColor;
    try {
      if (subject.color.isNotEmpty) {
        final colorString = subject.color.replaceAll('#', '');
        subjectColor = Color(int.parse('FF$colorString', radix: 16));
      }
    } catch (e) {
      // استخدام اللون الافتراضي في حالة الخطأ
    }

    // اختيار أيقونة بناءً على اسم المادة
    IconData subjectIcon = Icons.book;
    if (subject.name.contains('رياضيات')) {
      subjectIcon = Icons.calculate;
    } else if (subject.name.contains('فيزياء')) {
      subjectIcon = Icons.science;
    } else if (subject.name.contains('كيمياء')) {
      subjectIcon = Icons.biotech;
    } else if (subject.name.contains('أحياء')) {
      subjectIcon = Icons.eco;
    } else if (subject.name.contains('عربية')) {
      subjectIcon = Icons.language;
    } else if (subject.name.contains('إنجليزية')) {
      subjectIcon = Icons.translate;
    }

    return Card(
      elevation: 4,
      shadowColor: subjectColor.withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: InkWell(
        onTap: () {
          // التحقق من الاشتراك قبل الدخول
          if (isSubscribed) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => SubjectDetailPage(subject: subject),
              ),
            );
          } else {
            _showSubscriptionRequiredDialog(subject.id);
          }
        },
        borderRadius: BorderRadius.circular(16.r),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            gradient: LinearGradient(
              colors: isSubscribed
                  ? [
                      subjectColor.withValues(alpha: 0.1),
                      subjectColor.withValues(alpha: 0.05),
                    ]
                  : [
                      Colors.grey.withValues(alpha: 0.1),
                      Colors.grey.withValues(alpha: 0.05),
                    ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Stack(
            children: [
              Padding(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 60.w,
                      height: 60.h,
                      decoration: BoxDecoration(
                        color: isSubscribed ? subjectColor : Colors.grey,
                        borderRadius: BorderRadius.circular(30.r),
                      ),
                      child: Center(
                        child: Icon(
                          subjectIcon,
                          color: Colors.white,
                          size: 30.sp,
                        ),
                      ),
                    ),
                    SizedBox(height: 12.h),
                    Text(
                      subject.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isSubscribed
                            ? AppTheme.textPrimaryColor
                            : AppTheme.textSecondaryColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 8.h),
                    Center(
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: isSubscribed
                              ? subjectColor.withValues(alpha: 0.2)
                              : Colors.grey.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Text(
                          isSubscribed ? 'متاح' : 'مقفل',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: isSubscribed
                                    ? subjectColor
                                    : Colors.grey,
                                fontWeight: FontWeight.w500,
                              ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // أيقونة القفل للمواد غير المشترك بها
              if (!isSubscribed)
                Positioned(
                  top: 8.w,
                  right: 8.w,
                  child: Container(
                    padding: EdgeInsets.all(4.w),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(Icons.lock, color: Colors.red, size: 16.sp),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// عرض حوار يطلب الاشتراك في المادة
  void _showSubscriptionRequiredDialog(String subjectId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.lock, color: Colors.red),
            SizedBox(width: 8.w),
            Text('مادة مقفلة'),
          ],
        ),
        content: Text('يرجى تفعيل اشتراك لفتح هذه المادة والوصول إلى محتواها.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // الانتقال إلى صفحة الاشتراك
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => SubscriptionActivationPage(),
                ),
              );
            },
            child: Text('تفعيل الاشتراك'),
          ),
        ],
      ),
    );
  }
}
