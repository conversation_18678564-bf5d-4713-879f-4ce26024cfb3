import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/models/question_model.dart';
import '../../../../shared/services/exam_service.dart';
import 'questions_viewer_page.dart';
import 'subject_units_page.dart';

class SubjectSearchPage extends StatefulWidget {
  final Subject subject;

  const SubjectSearchPage({super.key, required this.subject});

  @override
  State<SubjectSearchPage> createState() => _SubjectSearchPageState();
}

class _SubjectSearchPageState extends State<SubjectSearchPage> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  List<Question> _searchResults = [];
  bool _isSearching = false;
  bool _hasSearched = false;
  String _currentQuery = '';

  @override
  void initState() {
    super.initState();
    // التركيز على حقل البحث عند فتح الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _hasSearched = false;
        _currentQuery = '';
      });
      return;
    }

    setState(() {
      _isSearching = true;
      _currentQuery = query.trim();
    });

    try {
      final results = await ExamService.instance.searchQuestions(
        query.trim(),
        widget.subject.id,
      );

      setState(() {
        _searchResults = results;
        _isSearching = false;
        _hasSearched = true;
      });
    } catch (e) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
        _hasSearched = true;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء البحث: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _openQuestion(Question question) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => QuestionsViewerPage(
          title: 'نتيجة البحث',
          subject: widget.subject,
          questionType: QuestionFilterType.single,
          singleQuestionId: question.id,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: _getSubjectColor(),
        elevation: 0,
        title: Text(
          'البحث في ${widget.subject.name}',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Column(
        children: [
          // شريط البحث
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: _getSubjectColor(),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20.r),
                bottomRight: Radius.circular(20.r),
              ),
            ),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(25.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                focusNode: _searchFocusNode,
                textDirection: TextDirection.rtl,
                decoration: InputDecoration(
                  hintText: 'ابحث في الأسئلة والإجابات...',
                  hintStyle: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 14.sp,
                  ),
                  prefixIcon: _isSearching
                      ? Padding(
                          padding: EdgeInsets.all(12.w),
                          child: SizedBox(
                            width: 20.w,
                            height: 20.h,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                _getSubjectColor(),
                              ),
                            ),
                          ),
                        )
                      : Icon(
                          Icons.search,
                          color: _getSubjectColor(),
                          size: 24.w,
                        ),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            _searchController.clear();
                            _performSearch('');
                          },
                          icon: Icon(
                            Icons.clear,
                            color: Colors.grey[500],
                            size: 20.w,
                          ),
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25.r),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 20.w,
                    vertical: 12.h,
                  ),
                ),
                onChanged: (value) {
                  setState(() {}); // لتحديث أيقونة المسح
                  // البحث التلقائي بعد توقف الكتابة لثانية واحدة
                  Future.delayed(const Duration(milliseconds: 800), () {
                    if (_searchController.text == value) {
                      _performSearch(value);
                    }
                  });
                },
                onSubmitted: _performSearch,
              ),
            ),
          ),

          // النتائج
          Expanded(child: _buildSearchResults()),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (!_hasSearched) {
      return _buildInitialState();
    }

    if (_isSearching) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(_getSubjectColor()),
            ),
            SizedBox(height: 16.h),
            Text(
              'جاري البحث...',
              style: TextStyle(fontSize: 16.sp, color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    if (_searchResults.isEmpty) {
      return _buildNoResults();
    }

    return _buildResultsList();
  }

  Widget _buildInitialState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search, size: 80.w, color: Colors.grey[300]),
          SizedBox(height: 16.h),
          Text(
            'ابحث في أسئلة ${widget.subject.name}',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'يمكنك البحث في نص الأسئلة والإجابات والشروحات',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNoResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search_off, size: 80.w, color: Colors.grey[300]),
          SizedBox(height: 16.h),
          Text(
            'لا توجد نتائج',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'لم يتم العثور على أسئلة تحتوي على "$_currentQuery"',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          Text(
            'جرب كلمات مختلفة أو تأكد من الإملاء',
            style: TextStyle(fontSize: 12.sp, color: Colors.grey[400]),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عداد النتائج
        Padding(
          padding: EdgeInsets.all(16.w),
          child: Text(
            'تم العثور على ${_searchResults.length} نتيجة لـ "$_currentQuery"',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ),

        // قائمة النتائج
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            itemCount: _searchResults.length,
            itemBuilder: (context, index) {
              final question = _searchResults[index];
              return _buildQuestionCard(question);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildQuestionCard(Question question) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: InkWell(
        onTap: () => _openQuestion(question),
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // نص السؤال
              Text(
                question.questionText,
                style: TextStyle(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
                textDirection: TextDirection.rtl,
              ),

              if (question.options.isNotEmpty) ...[
                SizedBox(height: 12.h),
                // عرض أول إجابتين كمعاينة
                ...question.options
                    .take(2)
                    .map(
                      (option) => Padding(
                        padding: EdgeInsets.only(bottom: 4.h),
                        child: Row(
                          children: [
                            Container(
                              width: 6.w,
                              height: 6.h,
                              decoration: BoxDecoration(
                                color: Colors.grey[400],
                                shape: BoxShape.circle,
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Expanded(
                              child: Text(
                                option,
                                style: TextStyle(
                                  fontSize: 13.sp,
                                  color: Colors.grey[700],
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                textDirection: TextDirection.rtl,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                if (question.options.length > 2)
                  Text(
                    '... و ${question.options.length - 2} إجابات أخرى',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[500],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
              ],

              SizedBox(height: 12.h),
              // معلومات إضافية
              Row(
                children: [
                  Icon(
                    Icons.quiz_outlined,
                    size: 16.w,
                    color: _getSubjectColor(),
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    _getQuestionTypeText(question.type),
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: _getSubjectColor(),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 14.w,
                    color: Colors.grey[400],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getQuestionTypeText(QuestionType type) {
    switch (type) {
      case QuestionType.multipleChoice:
        return 'اختيار متعدد';
      case QuestionType.trueFalse:
        return 'صح أم خطأ';
      case QuestionType.shortAnswer:
        return 'إجابة قصيرة';
      case QuestionType.essay:
        return 'مقال';
      default:
        return 'سؤال';
    }
  }

  Color _getSubjectColor() {
    final name = widget.subject.name.toLowerCase();
    if (name.contains('رياضيات') || name.contains('math')) {
      return const Color(0xFF2196F3);
    } else if (name.contains('فيزياء') || name.contains('physics')) {
      return const Color(0xFF9C27B0);
    } else if (name.contains('كيمياء') || name.contains('chemistry')) {
      return const Color(0xFF4CAF50);
    } else if (name.contains('أحياء') || name.contains('biology')) {
      return const Color(0xFF8BC34A);
    } else if (name.contains('عربية') || name.contains('arabic')) {
      return const Color(0xFFFF9800);
    } else if (name.contains('إنجليزية') || name.contains('english')) {
      return const Color(0xFFF44336);
    }
    return AppTheme.primaryColor;
  }
}
