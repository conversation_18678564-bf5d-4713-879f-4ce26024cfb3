import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_model.dart';
import '../../../../shared/services/encryption_service.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/utils/video_player_wrapper.dart';

/// صفحة مشغل الفيديو مع دعم الجودات المتعددة والتحكم في السرعة
class VideoPlayerPage extends StatefulWidget {
  final Video video;
  final VideoQuality? preferredQuality;
  final bool isOfflineMode;

  const VideoPlayerPage({
    super.key,
    required this.video,
    this.preferredQuality,
    this.isOfflineMode = false,
  });

  @override
  State<VideoPlayerPage> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  VideoPlayerController? _controller;
  ChewieController? _chewieController;
  final VideoService _videoService = VideoService.instance;
  final EncryptionService _encryptionService = EncryptionService.instance;

  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  bool _showControls = true;
  bool _isFullscreen = false;

  VideoQuality _currentQuality = VideoQuality.auto;
  double _currentSpeed = 1.0;
  double _normalSpeed = 1.0; // السرعة العادية قبل التسريع
  bool _isSpeedBoostActive = false; // هل التسريع بالضغط المطول نشط

  final List<double> _speedOptions = [0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0];

  // متغيرات للتحكم في النقرات المتعددة
  Timer? _tapTimer;
  int _tapCount = 0;

  // متغيرات للتحكم في إخفاء الأزرار
  Timer? _hideControlsTimer;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  @override
  void dispose() {
    _chewieController?.dispose();
    _controller?.dispose();
    _tapTimer?.cancel();
    _hideControlsTimer?.cancel();
    if (_isFullscreen) {
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.manual,
        overlays: SystemUiOverlay.values,
      );
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    }
    super.dispose();
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // تحديد الجودة المناسبة
      if (widget.preferredQuality != null) {
        _currentQuality = widget.preferredQuality!;
      } else if (_currentQuality == VideoQuality.auto) {
        _currentQuality = widget.video.bestAvailableQuality;
      }

      // التحقق من وجود ملف محلي أولاً
      if (widget.isOfflineMode ||
          await _videoService.isVideoDownloadedWithQuality(
            widget.video.id,
            _currentQuality,
          )) {
        debugPrint(
          '🎬 تشغيل فيديو محلي: ${widget.video.title} - ${_currentQuality.name}',
        );

        // الحصول على الفيديو المفكوك التشفير من الخدمة
        final decryptedVideoPath = await _videoService
            .getDecryptedVideoForPlayback(widget.video.id, _currentQuality);

        if (decryptedVideoPath != null) {
          // إنشاء مشغل الفيديو من الملف المحلي باستخدام wrapper
          _controller = VideoPlayerWrapper.createController(
            videoPath: decryptedVideoPath,
            networkUrl: null,
            isLocalFile: true,
          );
          debugPrint(
            '✅ تم إنشاء مشغل من الملف المفكوك التشفير: $decryptedVideoPath',
          );
        } else {
          throw Exception('فشل في فك تشفير الملف المحلي');
        }
      } else {
        debugPrint(
          '🌐 تشغيل فيديو عبر الإنترنت: ${widget.video.title} - ${_currentQuality.name}',
        );

        // الحصول على الرابط المشفر
        final encryptedUrl = widget.video.getEncryptedUrlForQuality(
          _currentQuality,
        );
        if (encryptedUrl == null || encryptedUrl.isEmpty) {
          throw Exception('لا يوجد رابط متاح لهذه الجودة');
        }

        // فك تشفير الرابط
        final decryptedUrl = _encryptionService.decryptText(encryptedUrl);

        // إنشاء مشغل الفيديو من الإنترنت باستخدام wrapper
        debugPrint('🌐 محاولة تشغيل الرابط: $decryptedUrl');
        _controller = VideoPlayerWrapper.createController(
          videoPath: null,
          networkUrl: decryptedUrl,
          isLocalFile: false,
        );
      }

      // تهيئة المشغل مع معالجة خاصة لـ Windows
      if (_controller != null) {
        try {
          debugPrint('🔄 بدء تهيئة مشغل الفيديو...');

          // تهيئة video_player
          await _controller!.initialize().timeout(
            const Duration(seconds: 30),
            onTimeout: () {
              throw Exception('انتهت مهلة تحميل الفيديو (30 ثانية)');
            },
          );

          // إنشاء chewie controller
          _chewieController = ChewieController(
            videoPlayerController: _controller!,
            autoPlay: true,
            looping: false,
            allowFullScreen: true,
            allowMuting: true,
            allowPlaybackSpeedChanging: true,
            showControls: true,
            materialProgressColors: ChewieProgressColors(
              playedColor: AppTheme.primaryColor,
              handleColor: AppTheme.primaryColor,
              backgroundColor: Colors.grey,
              bufferedColor: AppTheme.primaryColor.withValues(alpha: 0.3),
            ),
            placeholder: Container(
              color: Colors.black,
              child: const Center(
                child: CircularProgressIndicator(color: Colors.white),
              ),
            ),
            errorBuilder: (context, errorMessage) {
              return Container(
                color: Colors.black,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error, color: Colors.red, size: 48.sp),
                      SizedBox(height: 16.h),
                      Text(
                        'خطأ في تشغيل الفيديو',
                        style: TextStyle(color: Colors.white, fontSize: 16.sp),
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        errorMessage,
                        style: TextStyle(color: Colors.grey, fontSize: 12.sp),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              );
            },
          );

          // التحقق من أن الفيديو تم تحميله بنجاح
          if (_controller!.value.hasError) {
            throw Exception(
              'فشل في تحميل الفيديو: ${_controller!.value.errorDescription}',
            );
          }

          // التحقق من أن الفيديو له مدة صحيحة
          if (_controller!.value.duration == Duration.zero) {
            debugPrint(
              '⚠️ تحذير: مدة الفيديو صفر، قد يكون هناك مشكلة في الملف',
            );
          }

          _controller!.setPlaybackSpeed(_currentSpeed);

          // بدء التشغيل التلقائي
          _controller!.play();

          // بدء مؤقت إخفاء الأزرار
          _startHideControlsTimer();

          debugPrint('✅ تم تهيئة مشغل الفيديو بنجاح');
          debugPrint('📹 مدة الفيديو: ${_controller!.value.duration}');
          debugPrint(
            '📐 نسبة العرض للارتفاع: ${_controller!.value.aspectRatio}',
          );
        } catch (e) {
          debugPrint('❌ خطأ في تهيئة مشغل الفيديو: $e');

          // معلومات إضافية للتشخيص
          debugPrint('🔍 نوع المنصة: ${Platform.operatingSystem}');
          debugPrint(
            '🔍 وضع التشغيل: ${_controller is VideoPlayerController ? "عبر الإنترنت" : "محلي"}',
          );

          throw Exception('فشل في تهيئة مشغل الفيديو: $e');
        }
      } else {
        throw Exception('فشل في إنشاء مشغل الفيديو');
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      // تحديد رسالة الخطأ بناءً على نوع الخطأ والوضع
      String errorMessage;

      debugPrint('❌ خطأ تفصيلي في تحميل الفيديو: $e');
      debugPrint('🔍 نوع المنصة: ${Platform.operatingSystem}');
      debugPrint(
        '🔍 وضع التشغيل: ${widget.isOfflineMode ? "محلي" : "عبر الإنترنت"}',
      );

      if (!VideoPlayerWrapper.isVideoPlaybackSupported()) {
        // معالجة للمنصات غير المدعومة
        errorMessage = VideoPlayerWrapper.getUnsupportedPlatformMessage();
        debugPrint(
          '❌ المنصة غير مدعومة: ${VideoPlayerWrapper.getPlatformInfo()}',
        );
      } else {
        // معالجة عادية للمنصات الأخرى
        if (widget.isOfflineMode ||
            await _videoService.isVideoDownloadedWithQuality(
              widget.video.id,
              _currentQuality,
            )) {
          errorMessage = 'خطأ في تشغيل الفيديو المحمل';
        } else {
          errorMessage = 'تأكد من الاتصال بالانترنت';
        }
      }

      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = errorMessage;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: _isFullscreen
          ? null
          : AppBar(
              title: Text(
                widget.video.title,
                style: const TextStyle(color: Colors.white),
              ),
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
              actions: [
                IconButton(
                  icon: const Icon(Icons.fullscreen),
                  onPressed: _toggleFullscreen,
                ),
              ],
            ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }

    if (_hasError) {
      return _buildErrorWidget();
    }

    if (_chewieController == null) {
      return const Center(
        child: Text(
          'خطأ في تحميل الفيديو',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    return GestureDetector(
      onTapDown: _handleTapDown,
      onLongPressStart: _handleLongPressStart,
      onLongPressEnd: _handleLongPressEnd,
      child: Stack(
        children: [
          Center(
            child: _chewieController != null
                ? Chewie(controller: _chewieController!)
                : Container(
                    height: 200,
                    color: Colors.black,
                    child: const Center(child: CircularProgressIndicator()),
                  ),
          ),

          // مؤشر التسريع
          if (_isSpeedBoostActive)
            Positioned(
              top: 50.h,
              right: 20.w,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.fast_forward, color: Colors.white, size: 16.sp),
                    SizedBox(width: 4.w),
                    Text(
                      '${(_normalSpeed * 2).toStringAsFixed(1)}x',
                      style: TextStyle(color: Colors.white, fontSize: 14.sp),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: Colors.white),
          SizedBox(height: 16.h),
          Text(
            _errorMessage,
            style: TextStyle(color: Colors.white, fontSize: 16.sp),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _initializePlayer,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildControls() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withValues(alpha: 0.7),
            Colors.transparent,
            Colors.transparent,
            Colors.black.withValues(alpha: 0.7),
          ],
        ),
      ),
      child: Column(
        children: [
          if (_isFullscreen) _buildTopControls(),
          const Spacer(),
          _buildBottomControls(),
        ],
      ),
    );
  }

  Widget _buildTopControls() {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: _exitFullscreen,
            ),
            Expanded(
              child: Text(
                widget.video.title,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            _buildQualityButton(),
            SizedBox(width: 8.w),
            _buildSpeedButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            _buildProgressBar(),
            SizedBox(height: 16.h),
            Row(
              children: [
                IconButton(
                  icon: Icon(
                    _controller!.value.isPlaying
                        ? Icons.pause
                        : Icons.play_arrow,
                    color: Colors.white,
                    size: 32.sp,
                  ),
                  onPressed: _togglePlayPause,
                ),
                SizedBox(width: 16.w),
                Text(
                  _formatDuration(_controller!.value.position),
                  style: TextStyle(color: Colors.white, fontSize: 14.sp),
                ),
                const Spacer(),
                if (!_isFullscreen) ...[
                  _buildQualityButton(),
                  SizedBox(width: 8.w),
                  _buildSpeedButton(),
                  SizedBox(width: 8.w),
                ],
                Text(
                  _formatDuration(_controller!.value.duration),
                  style: TextStyle(color: Colors.white, fontSize: 14.sp),
                ),
                SizedBox(width: 16.w),
                IconButton(
                  icon: Icon(
                    _isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
                    color: Colors.white,
                  ),
                  onPressed: _toggleFullscreen,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressBar() {
    return GestureDetector(
      onTapDown: (details) {
        // إيقاف مؤقت إخفاء الأزرار عند التفاعل مع شريط التقدم
        _hideControlsTimer?.cancel();
      },
      onTapUp: (details) {
        // إعادة تشغيل مؤقت إخفاء الأزرار
        _startHideControlsTimer();
      },
      child: VideoProgressIndicator(
        _controller!,
        allowScrubbing: true,
        colors: const VideoProgressColors(
          playedColor: AppTheme.primaryColor,
          bufferedColor: Colors.grey,
          backgroundColor: Colors.white24,
        ),
      ),
    );
  }

  Widget _buildQualityButton() {
    return PopupMenuButton<VideoQuality>(
      icon: const Icon(Icons.high_quality, color: Colors.white),
      onSelected: _changeQuality,
      itemBuilder: (context) {
        return widget.video.availableQualities.map((quality) {
          return PopupMenuItem<VideoQuality>(
            value: quality,
            child: Row(
              children: [
                if (quality == _currentQuality)
                  const Icon(Icons.check, color: AppTheme.primaryColor),
                SizedBox(width: 8.w),
                Text(widget.video.getQualityDisplayText(quality)),
              ],
            ),
          );
        }).toList();
      },
    );
  }

  Widget _buildSpeedButton() {
    return PopupMenuButton<double>(
      icon: const Icon(Icons.speed, color: Colors.white),
      onSelected: _changeSpeed,
      itemBuilder: (context) {
        return _speedOptions.map((speed) {
          return PopupMenuItem<double>(
            value: speed,
            child: Row(
              children: [
                if (speed == _currentSpeed)
                  const Icon(Icons.check, color: AppTheme.primaryColor),
                SizedBox(width: 8.w),
                Text('${speed}x'),
              ],
            ),
          );
        }).toList();
      },
    );
  }

  /// التعامل مع النقرات على الشاشة
  void _handleTapDown(TapDownDetails details) {
    final screenWidth = MediaQuery.of(context).size.width;
    final tapPosition = details.globalPosition;

    _tapCount++;

    // إلغاء المؤقت السابق إذا كان موجوداً
    _tapTimer?.cancel();

    // إنشاء مؤقت جديد للتحقق من عدد النقرات
    _tapTimer = Timer(const Duration(milliseconds: 300), () {
      if (_tapCount == 1) {
        // نقرة واحدة - إظهار/إخفاء الأزرار
        _toggleControls();
      } else if (_tapCount == 2) {
        // نقرتان متتاليتان
        _handleDoubleTap(tapPosition, screenWidth);
      }

      // إعادة تعيين العداد
      _tapCount = 0;
    });
  }

  /// التعامل مع النقرات المزدوجة
  void _handleDoubleTap(Offset tapPosition, double screenWidth) {
    final leftThird = screenWidth / 3;
    final rightThird = screenWidth * 2 / 3;

    if (tapPosition.dx < leftThird) {
      // النقر على الجزء الأيسر - إرجاع 5 ثواني
      _seekRelative(-5);
      _showSeekIndicator('إرجاع 5 ثواني', Icons.replay_5);
    } else if (tapPosition.dx > rightThird) {
      // النقر على الجزء الأيمن - تقديم 10 ثواني
      _seekRelative(10);
      _showSeekIndicator('تقديم 10 ثواني', Icons.forward_10);
    } else {
      // النقر على الجزء الأوسط - إيقاف/تشغيل
      _togglePlayPause();
    }
  }

  /// التقديم أو الإرجاع بعدد ثواني محدد
  void _seekRelative(int seconds) {
    if (_controller != null) {
      final currentPosition = _controller!.value.position;
      final newPosition = currentPosition + Duration(seconds: seconds);
      final maxDuration = _controller!.value.duration;

      // التأكد من عدم تجاوز حدود الفيديو
      if (newPosition < Duration.zero) {
        _controller!.seekTo(Duration.zero);
      } else if (newPosition > maxDuration) {
        _controller!.seekTo(maxDuration);
      } else {
        _controller!.seekTo(newPosition);
      }
    }
  }

  /// إظهار مؤشر التقديم/الإرجاع
  void _showSeekIndicator(String text, IconData icon) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white),
            SizedBox(width: 8.w),
            Text(text),
          ],
        ),
        duration: const Duration(seconds: 1),
        backgroundColor: Colors.black.withValues(alpha: 0.8),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// بدء التسريع بالضغط المطول
  void _handleLongPressStart(LongPressStartDetails details) {
    if (_controller != null && !_isSpeedBoostActive) {
      setState(() {
        _normalSpeed = _currentSpeed;
        _isSpeedBoostActive = true;
        _currentSpeed = _normalSpeed * 2; // مضاعفة السرعة
      });
      _controller!.setPlaybackSpeed(_currentSpeed);
    }
  }

  /// إنهاء التسريع بالضغط المطول
  void _handleLongPressEnd(LongPressEndDetails details) {
    if (_controller != null && _isSpeedBoostActive) {
      setState(() {
        _currentSpeed = _normalSpeed;
        _isSpeedBoostActive = false;
      });
      _controller!.setPlaybackSpeed(_currentSpeed);
    }
  }

  void _togglePlayPause() {
    setState(() {
      if (_controller!.value.isPlaying) {
        _controller!.pause();
      } else {
        _controller!.play();
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    if (_showControls) {
      _startHideControlsTimer();
    }
  }

  /// بدء مؤقت إخفاء الأزرار
  void _startHideControlsTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  /// إظهار الأزرار مؤقتاً
  void _showControlsTemporarily() {
    setState(() {
      _showControls = true;
    });
    _startHideControlsTimer();
  }

  void _toggleFullscreen() {
    if (_isFullscreen) {
      _exitFullscreen();
    } else {
      _enterFullscreen();
    }
  }

  void _enterFullscreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

    // تحديد الاتجاه حسب نسبة العرض إلى الارتفاع للفيديو
    if (_controller != null) {
      final aspectRatio = _controller!.value.aspectRatio;

      if (aspectRatio > 1.0) {
        // فيديو أفقي - استخدام الوضع الأفقي
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight,
        ]);
      } else {
        // فيديو عمودي - استخدام الوضع العمودي
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
        ]);
      }
    } else {
      // افتراضي - الوضع الأفقي
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }

    setState(() {
      _isFullscreen = true;
    });
  }

  void _exitFullscreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    setState(() {
      _isFullscreen = false;
    });
  }

  void _changeQuality(VideoQuality quality) {
    if (quality != _currentQuality) {
      final currentPosition = _controller!.value.position;
      final wasPlaying = _controller!.value.isPlaying;

      setState(() {
        _currentQuality = quality;
      });

      _controller!.dispose();
      _initializePlayer().then((_) {
        if (!_hasError && _controller != null) {
          _controller!.seekTo(currentPosition);
          if (wasPlaying) {
            _controller!.play();
          }
        }
      });
    }
  }

  void _changeSpeed(double speed) {
    setState(() {
      _currentSpeed = speed;
      _normalSpeed = speed; // تحديث السرعة العادية أيضاً
      _controller!.setPlaybackSpeed(speed);
    });
    _showControlsTemporarily();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    if (duration.inHours > 0) {
      return '$hours:$minutes:$seconds';
    } else {
      return '$minutes:$seconds';
    }
  }
}
