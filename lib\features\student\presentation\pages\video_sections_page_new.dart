import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_section_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../../../shared/widgets/error_widget.dart';

/// صفحة أقسام الفيديوهات مع التخزين الدائم
class VideoSectionsPageNew extends StatefulWidget {
  const VideoSectionsPageNew({super.key});

  @override
  State<VideoSectionsPageNew> createState() => _VideoSectionsPageNewState();
}

class _VideoSectionsPageNewState extends State<VideoSectionsPageNew> {
  final VideoService _videoService = VideoService.instance;
  List<VideoSection> _sections = [];
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadSections();
  }

  /// تحميل الأقسام من التخزين الدائم أولاً
  Future<void> _loadSections() async {
    try {
      // تحميل البيانات المحفوظة دائمياً فوراً
      final sections = await _videoService.getPaidVideoSections();

      if (mounted) {
        setState(() {
          _sections = sections;
          _error = null;
        });
      }

      // تحديث في الخلفية إذا لزم الأمر (بدون انتظار)
      _updateInBackground();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل أقسام الفيديوهات: $e');
      if (mounted) {
        setState(() {
          _error = 'خطأ في تحميل البيانات';
        });
      }
    }
  }

  /// تحديث البيانات في الخلفية
  Future<void> _updateInBackground() async {
    try {
      // فحص الحاجة للتحديث (كل 6 ساعات)
      final needsUpdate = await _videoService.persistentStorage.needsUpdate(
        'video_sections',
      );

      if (needsUpdate) {
        debugPrint('🔄 تحديث أقسام الفيديوهات في الخلفية...');
        await _videoService.refreshVideoSectionsFromFirebase();

        // إعادة تحميل البيانات المحدثة
        final updatedSections = await _videoService.getPaidVideoSections();

        if (mounted) {
          setState(() {
            _sections = updatedSections;
          });
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحديث في الخلفية: $e');
      // لا نعرض خطأ للمستخدم لأن التحديث في الخلفية
    }
  }

  /// تحديث يدوي بالسحب للأسفل
  Future<void> _onRefresh() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      await _videoService.refreshVideoSectionsFromFirebase();
      final sections = await _videoService.getPaidVideoSections();

      if (mounted) {
        setState(() {
          _sections = sections;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث أقسام الفيديوهات: $e');
      if (mounted) {
        setState(() {
          _error = 'خطأ في تحديث البيانات';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: CustomAppBar(title: 'أقسام الفيديوهات', showBackButton: true),
      body: RefreshIndicator(
        onRefresh: _onRefresh,
        color: AppTheme.primaryColor,
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (_error != null && _sections.isEmpty) {
      return CustomErrorWidget(message: _error!, onRetry: _loadSections);
    }

    if (_sections.isEmpty && !_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.video_library_outlined, size: 80.sp, color: Colors.grey),
            SizedBox(height: 16.h),
            Text(
              'لا توجد أقسام فيديوهات متاحة',
              style: TextStyle(fontSize: 16.sp, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        if (_isLoading)
          Container(
            padding: EdgeInsets.all(16.w),
            child: const LoadingWidget(),
          ),
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.all(16.w),
            itemCount: _sections.length,
            itemBuilder: (context, index) {
              final section = _sections[index];
              return _buildSectionCard(section);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSectionCard(VideoSection section) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16.r),
          onTap: () => _navigateToSubjects(section),
          child: Padding(
            padding: EdgeInsets.all(20.w),
            child: Row(
              children: [
                // أيقونة القسم
                Container(
                  width: 60.w,
                  height: 60.w,
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(
                    Icons.video_library,
                    color: Colors.white,
                    size: 30.sp,
                  ),
                ),
                SizedBox(width: 16.w),
                // معلومات القسم
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        section.name,
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      if (section.description.isNotEmpty) ...[
                        SizedBox(height: 8.h),
                        Text(
                          section.description,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.black54,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                // سهم الانتقال
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppTheme.primaryColor,
                  size: 20.sp,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToSubjects(VideoSection section) {
    // TODO: إنشاء صفحة المواد
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('سيتم فتح مواد القسم: ${section.name}'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }
}
