import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart' as media_kit_video;
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_model.dart' as video_model;
import '../../../../shared/services/encryption_service.dart';
import '../../../../shared/services/video_service.dart';

/// صفحة مشغل الفيديو للـ Windows باستخدام media_kit
class WindowsVideoPlayerPage extends StatefulWidget {
  final video_model.Video video;
  final video_model.VideoQuality? preferredQuality;
  final bool isOfflineMode;

  const WindowsVideoPlayerPage({
    super.key,
    required this.video,
    this.preferredQuality,
    this.isOfflineMode = false,
  });

  @override
  State<WindowsVideoPlayerPage> createState() => _WindowsVideoPlayerPageState();
}

class _WindowsVideoPlayerPageState extends State<WindowsVideoPlayerPage> {
  late Player _player;
  late media_kit_video.VideoController _controller;
  final VideoService _videoService = VideoService.instance;
  final EncryptionService _encryptionService = EncryptionService.instance;

  video_model.VideoQuality _currentQuality =
      video_model.VideoQuality.quality480;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _currentQuality =
        widget.preferredQuality ?? video_model.VideoQuality.quality480;
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // إنشاء player
      _player = Player();
      _controller = media_kit_video.VideoController(_player);

      String? videoSource;

      // التحقق من وجود ملف محلي أولاً
      if (widget.isOfflineMode ||
          await _videoService.isVideoDownloadedWithQuality(
            widget.video.id,
            _currentQuality,
          )) {
        debugPrint('🎬 تشغيل فيديو محلي: ${widget.video.title}');

        final decryptedVideoPath = await _videoService
            .getDecryptedVideoForPlayback(widget.video.id, _currentQuality);

        if (decryptedVideoPath != null) {
          videoSource = decryptedVideoPath;
          debugPrint('✅ مسار الفيديو المحلي: $videoSource');
        } else {
          throw Exception('فشل في فك تشفير الملف المحلي');
        }
      } else {
        debugPrint('🌐 تشغيل فيديو عبر الإنترنت: ${widget.video.title}');

        final encryptedUrl = widget.video.getEncryptedUrlForQuality(
          _currentQuality,
        );
        if (encryptedUrl == null || encryptedUrl.isEmpty) {
          throw Exception('لا يوجد رابط متاح لهذه الجودة');
        }

        final decryptedUrl = _encryptionService.decryptText(encryptedUrl);
        videoSource = decryptedUrl;
        debugPrint('🌐 رابط الفيديو: $videoSource');
      }

      if (videoSource != null && videoSource.isNotEmpty) {
        // تشغيل الفيديو
        await _player.open(Media(videoSource));

        setState(() {
          _isLoading = false;
        });

        debugPrint('✅ تم تهيئة مشغل الفيديو بنجاح');
      } else {
        throw Exception('لا يوجد مصدر فيديو صالح');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة مشغل الفيديو: $e');
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'خطأ في تشغيل الفيديو: $e';
      });
    }
  }

  @override
  void dispose() {
    _player.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          widget.video.title,
          style: TextStyle(
            color: Colors.white,
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings, color: Colors.white),
            onPressed: _showQualitySelector,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64.sp, color: Colors.red),
            SizedBox(height: 16.h),
            Text(
              _errorMessage,
              style: TextStyle(color: Colors.white, fontSize: 16.sp),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: _initializePlayer,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return media_kit_video.Video(controller: _controller);
  }

  void _showQualitySelector() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر جودة الفيديو'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: video_model.VideoQuality.values.map((quality) {
            final isAvailable =
                widget.video.getEncryptedUrlForQuality(quality) != null;
            return ListTile(
              title: Text(quality.name),
              trailing: _currentQuality == quality
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              enabled: isAvailable,
              onTap: isAvailable
                  ? () {
                      Navigator.pop(context);
                      _changeQuality(quality);
                    }
                  : null,
            );
          }).toList(),
        ),
      ),
    );
  }

  Future<void> _changeQuality(video_model.VideoQuality newQuality) async {
    if (newQuality == _currentQuality) return;

    setState(() {
      _currentQuality = newQuality;
    });

    await _initializePlayer();
  }
}
