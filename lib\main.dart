import 'dart:io';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'shared/utils/media_kit_wrapper.dart';

import 'flavors.dart';
import 'app.dart';
import 'firebase_options_student.dart';
import 'firebase_options_admin.dart' as admin_options;
import 'shared/services/encryption_service.dart';
import 'shared/services/video_service.dart';
import 'shared/services/section_service.dart';
import 'shared/services/content_service.dart';
import 'shared/services/local_questions_cache_service.dart';

void main() async {
  // معالجة الأخطاء العامة
  FlutterError.onError = (FlutterErrorDetails details) {
    print('🔴 خطأ Flutter: ${details.exception}');
    print('🔍 التفاصيل: ${details.stack}');
  };

  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة media_kit للـ Windows
  if (Platform.isWindows) {
    MediaKitWrapper.ensureInitialized();
    debugPrint('✅ MediaKit جاهز للاستخدام على Windows');
  } else {
    debugPrint('✅ video_player جاهز للاستخدام على Android');
  }

  try {
    // تحديد النكهة بناءً على متغير البيئة
    const flavor = String.fromEnvironment('FLAVOR', defaultValue: 'student');
    print('🚀 تشغيل التطبيق بنكهة: $flavor');

    if (flavor == 'admin') {
      F.appFlavor = Flavor.admin;
      await Firebase.initializeApp(
        options: admin_options.DefaultFirebaseOptions.currentPlatform,
      );
    } else {
      F.appFlavor = Flavor.student;
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
    }

    // تهيئة خدمة التشفير
    EncryptionService.instance.initialize();

    // تهيئة خدمة الفيديو
    await VideoService.instance.initialize();

    // تهيئة خدمة الأقسام
    await SectionService.instance.initialize();

    // تهيئة خدمة المحتوى
    await ContentService.instance.initialize();

    // تنظيف البيانات التالفة
    await LocalQuestionsCacheService.instance.cleanupOnStartup();

    runApp(const MyApp());
  } catch (e, stackTrace) {
    print('🔴 خطأ في تهيئة التطبيق: $e');
    print('🔍 Stack trace: $stackTrace');

    // تشغيل التطبيق حتى لو فشلت بعض الخدمات
    runApp(const MyApp());
  }
}
