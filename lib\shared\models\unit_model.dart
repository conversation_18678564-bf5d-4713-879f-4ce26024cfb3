import 'package:cloud_firestore/cloud_firestore.dart';

class Unit {
  final String id;
  final String subjectId;
  final String name;
  final String description;
  final int order;
  final String iconUrl;
  final String color;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdByAdminId;

  const Unit({
    required this.id,
    required this.subjectId,
    required this.name,
    required this.description,
    required this.order,
    required this.iconUrl,
    required this.color,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.createdByAdminId,
  });

  factory Unit.fromMap(Map<String, dynamic> map) {
    return Unit(
      id: map['id'] ?? '',
      subjectId: map['subjectId'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      order: map['order'] ?? 0,
      iconUrl: map['iconUrl'] ?? '',
      color: map['color'] ?? '',
      isActive: map['isActive'] ?? true,
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
      createdByAdminId: map['createdByAdminId'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'subjectId': subjectId,
      'name': name,
      'description': description,
      'order': order,
      'iconUrl': iconUrl,
      'color': color,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdByAdminId': createdByAdminId,
    };
  }

  Unit copyWith({
    String? id,
    String? subjectId,
    String? name,
    String? description,
    int? order,
    String? iconUrl,
    String? color,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdByAdminId,
  }) {
    return Unit(
      id: id ?? this.id,
      subjectId: subjectId ?? this.subjectId,
      name: name ?? this.name,
      description: description ?? this.description,
      order: order ?? this.order,
      iconUrl: iconUrl ?? this.iconUrl,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdByAdminId: createdByAdminId ?? this.createdByAdminId,
    );
  }
}
