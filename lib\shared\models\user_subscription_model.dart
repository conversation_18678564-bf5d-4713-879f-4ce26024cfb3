class UserSubscription {
  final String id;
  final String deviceId; // معرف الجهاز الفريد
  final List<String> subscribedSubjectIds; // المواد المشترك بها
  final List<String> videoSubjectIds; // مواد الفيديوهات المشترك بها
  final Map<String, DateTime> subjectActivationDates; // تاريخ تفعيل كل مادة
  final List<String> usedCodes; // الأكواد المستخدمة
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime expiresAt; // تاريخ انتهاء الاشتراك
  final bool isActive;

  UserSubscription({
    required this.id,
    required this.deviceId,
    this.subscribedSubjectIds = const [],
    this.videoSubjectIds = const [],
    this.subjectActivationDates = const {},
    this.usedCodes = const [],
    required this.createdAt,
    required this.updatedAt,
    required this.expiresAt,
    this.isActive = true,
  });

  factory UserSubscription.fromMap(Map<String, dynamic> map) {
    // تحويل subjectActivationDates من Map<String, int> إلى Map<String, DateTime>
    Map<String, DateTime> activationDates = {};
    if (map['subjectActivationDates'] != null) {
      Map<String, dynamic> datesMap = Map<String, dynamic>.from(
        map['subjectActivationDates'],
      );
      datesMap.forEach((key, value) {
        activationDates[key] = DateTime.fromMillisecondsSinceEpoch(value);
      });
    }

    return UserSubscription(
      id: map['id'] ?? '',
      deviceId: map['deviceId'] ?? '',
      subscribedSubjectIds: List<String>.from(
        map['subscribedSubjectIds'] ?? [],
      ),
      videoSubjectIds: List<String>.from(map['videoSubjectIds'] ?? []),
      subjectActivationDates: activationDates,
      usedCodes: List<String>.from(map['usedCodes'] ?? []),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt'] ?? 0),
      expiresAt: DateTime.fromMillisecondsSinceEpoch(map['expiresAt'] ?? 0),
      isActive: map['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toMap() {
    // تحويل subjectActivationDates من Map<String, DateTime> إلى Map<String, int>
    Map<String, int> activationDatesMap = {};
    subjectActivationDates.forEach((key, value) {
      activationDatesMap[key] = value.millisecondsSinceEpoch;
    });

    return {
      'id': id,
      'deviceId': deviceId,
      'subscribedSubjectIds': subscribedSubjectIds,
      'videoSubjectIds': videoSubjectIds,
      'subjectActivationDates': activationDatesMap,
      'usedCodes': usedCodes,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'expiresAt': expiresAt.millisecondsSinceEpoch,
      'isActive': isActive,
    };
  }

  bool isSubscribedToSubject(String subjectId) {
    // إذا كان المستخدم مشترك في "جميع المواد"، فهو مشترك في جميع المواد
    if (subscribedSubjectIds.contains('all_subjects')) {
      return true;
    }
    return subscribedSubjectIds.contains(subjectId);
  }

  DateTime? getSubjectActivationDate(String subjectId) {
    return subjectActivationDates[subjectId];
  }

  bool hasUsedCode(String code) {
    return usedCodes.contains(code);
  }

  UserSubscription copyWith({
    String? id,
    String? deviceId,
    List<String>? subscribedSubjectIds,
    List<String>? videoSubjectIds,
    Map<String, DateTime>? subjectActivationDates,
    List<String>? usedCodes,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? expiresAt,
    bool? isActive,
  }) {
    return UserSubscription(
      id: id ?? this.id,
      deviceId: deviceId ?? this.deviceId,
      subscribedSubjectIds: subscribedSubjectIds ?? this.subscribedSubjectIds,
      videoSubjectIds: videoSubjectIds ?? this.videoSubjectIds,
      subjectActivationDates:
          subjectActivationDates ?? this.subjectActivationDates,
      usedCodes: usedCodes ?? this.usedCodes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      isActive: isActive ?? this.isActive,
    );
  }

  // إضافة اشتراك جديد
  UserSubscription addSubscription(
    List<String> newSubjectIds,
    List<String> newVideoSubjectIds,
    String codeUsed,
    DateTime expiryDate,
  ) {
    final now = DateTime.now();
    final updatedSubjects = List<String>.from(subscribedSubjectIds);
    final updatedVideoSubjects = List<String>.from(videoSubjectIds);
    final updatedActivationDates = Map<String, DateTime>.from(
      subjectActivationDates,
    );
    final updatedUsedCodes = List<String>.from(usedCodes);

    // إضافة المواد الجديدة
    for (String subjectId in newSubjectIds) {
      if (!updatedSubjects.contains(subjectId)) {
        updatedSubjects.add(subjectId);
        updatedActivationDates[subjectId] = now;
      }
    }

    // إضافة مواد الفيديوهات الجديدة
    for (String videoSubjectId in newVideoSubjectIds) {
      if (!updatedVideoSubjects.contains(videoSubjectId)) {
        updatedVideoSubjects.add(videoSubjectId);
      }
    }

    // إضافة الكود المستخدم
    if (!updatedUsedCodes.contains(codeUsed)) {
      updatedUsedCodes.add(codeUsed);
    }

    return copyWith(
      subscribedSubjectIds: updatedSubjects,
      videoSubjectIds: updatedVideoSubjects,
      subjectActivationDates: updatedActivationDates,
      usedCodes: updatedUsedCodes,
      updatedAt: now,
      expiresAt: expiryDate,
    );
  }
}
