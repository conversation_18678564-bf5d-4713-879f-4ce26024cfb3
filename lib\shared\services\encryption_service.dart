import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:flutter/foundation.dart' hide Key;

/// خدمة التشفير والحماية للفيديوهات والروابط
class EncryptionService {
  static final EncryptionService _instance = EncryptionService._internal();
  static EncryptionService get instance => _instance;
  EncryptionService._internal();

  // مفتاح التشفير الثابت (يجب أن يكون آمناً في الإنتاج)
  static const String _encryptionKey = 'SmartTestVideoSecureKey2024!@#';

  late final Encrypter _encrypter;
  late final IV _iv;
  bool _isInitialized = false;

  /// تهيئة خدمة التشفير
  void initialize() {
    if (_isInitialized) {
      return; // تم التهيئة مسبقاً
    }
    // إنشاء مفتاح 256 بت (32 بايت) باستخدام SHA-256
    final keyBytes = sha256.convert(utf8.encode(_encryptionKey)).bytes;
    final key = Key(Uint8List.fromList(keyBytes));
    _encrypter = Encrypter(AES(key));
    _iv = IV.fromSecureRandom(16);
    _isInitialized = true;
  }

  /// تشفير نص (للروابط)
  String encryptText(String plainText) {
    try {
      final encrypted = _encrypter.encrypt(plainText, iv: _iv);
      return encrypted.base64;
    } catch (e) {
      throw Exception('فشل في تشفير النص: $e');
    }
  }

  /// التحقق من ما إذا كان النص مشفراً
  bool isEncrypted(String text) {
    try {
      // محاولة فك التشفير للتحقق
      Encrypted.fromBase64(text);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// تحويل رابط Google Drive إلى رابط تحميل مباشر
  String convertGoogleDriveUrl(String url) {
    try {
      // التحقق من أن الرابط من Google Drive
      if (url.contains('drive.google.com/file/d/')) {
        // استخراج معرف الملف من الرابط
        final regex = RegExp(r'/file/d/([a-zA-Z0-9_-]+)');
        final match = regex.firstMatch(url);

        if (match != null) {
          final fileId = match.group(1);
          // تحويل إلى رابط تحميل مباشر
          return 'https://drive.google.com/uc?export=download&id=$fileId';
        }
      }

      // إذا لم يكن رابط Google Drive أو لم نتمكن من تحويله، إرجاع الرابط الأصلي
      return url;
    } catch (e) {
      debugPrint('خطأ في تحويل رابط Google Drive: $e');
      return url;
    }
  }

  /// فك تشفير نص (للروابط) مع التحقق من التشفير أولاً
  String decryptText(String text) {
    try {
      // إذا لم يكن النص مشفراً، إرجاعه كما هو
      if (!isEncrypted(text)) {
        // تحويل رابط Google Drive إذا لزم الأمر
        return convertGoogleDriveUrl(text);
      }

      final encrypted = Encrypted.fromBase64(text);
      final decryptedText = _encrypter.decrypt(encrypted, iv: _iv);

      // تحويل رابط Google Drive إذا لزم الأمر
      return convertGoogleDriveUrl(decryptedText);
    } catch (e) {
      // تسجيل الخطأ للمطورين فقط مع معلومات إضافية للـ Windows
      if (!kIsWeb && Platform.isWindows) {
        debugPrint('خطأ في فك التشفير على Windows: $e');
        debugPrint('نوع النص: ${text.runtimeType}');
        debugPrint('طول النص: ${text.length}');
        debugPrint(
          'أول 50 حرف: ${text.length > 50 ? text.substring(0, 50) : text}',
        );
      } else {
        debugPrint('خطأ في فك التشفير: $e');
      }
      throw Exception('فشل في فك التشفير');
    }
  }

  /// تشفير بيانات الفيديو (للملفات المحملة) - طريقة بسيطة وموثوقة
  Uint8List encryptVideoData(Uint8List videoData) {
    try {
      // التأكد من تهيئة المشفر
      if (!_isInitialized) {
        throw Exception('خدمة التشفير غير مهيأة');
      }

      debugPrint('🔐 بدء تشفير الفيديو - الحجم: ${videoData.length} bytes');

      // تشفير البيانات كاملة بدون تقسيم
      final encrypted = _encrypter.encryptBytes(videoData, iv: _iv);

      debugPrint(
        '✅ تم تشفير الفيديو - الحجم المشفر: ${encrypted.bytes.length} bytes',
      );

      return encrypted.bytes;
    } catch (e) {
      debugPrint('❌ فشل في تشفير بيانات الفيديو: $e');
      throw Exception('فشل في تشفير بيانات الفيديو: $e');
    }
  }

  /// فك تشفير بيانات الفيديو (للملفات المحملة) - الطريقة القديمة للتوافق
  Uint8List decryptVideoData(Uint8List encryptedData) {
    try {
      // التأكد من تهيئة المشفر
      if (!_isInitialized) {
        throw Exception('خدمة التشفير غير مهيأة');
      }

      debugPrint(
        '🔓 بدء فك تشفير الفيديو - الحجم: ${encryptedData.length} bytes',
      );

      // فك تشفير البيانات كاملة
      final encrypted = Encrypted(encryptedData);
      final decryptedBytes = _encrypter.decryptBytes(encrypted, iv: _iv);
      final result = Uint8List.fromList(decryptedBytes);

      debugPrint('✅ تم فك تشفير الفيديو - الحجم: ${result.length} bytes');

      return result;
    } catch (e) {
      // تسجيل الخطأ مع معلومات إضافية للـ Windows
      if (!kIsWeb && Platform.isWindows) {
        debugPrint('❌ فشل في فك تشفير بيانات الفيديو على Windows: $e');
        debugPrint('حجم البيانات المشفرة: ${encryptedData.length} bytes');
        debugPrint('حالة التهيئة: $_isInitialized');
      } else {
        debugPrint('❌ فشل في فك تشفير بيانات الفيديو: $e');
      }
      throw Exception('فشل في فك تشفير بيانات الفيديو: $e');
    }
  }

  /// إنشاء hash للتحقق من سلامة البيانات
  String generateHash(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// التحقق من hash البيانات
  bool verifyHash(String data, String expectedHash) {
    final actualHash = generateHash(data);
    return actualHash == expectedHash;
  }

  /// إنشاء مفتاح فريد للجهاز
  String generateDeviceKey(String deviceId) {
    final combined = '$deviceId$_encryptionKey';
    final bytes = utf8.encode(combined);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 32);
  }

  /// تشفير رابط الفيديو مع معلومات إضافية
  Map<String, String> encryptVideoUrl(
    String url,
    String videoId,
    String deviceId,
  ) {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final deviceKey = generateDeviceKey(deviceId);

      // إنشاء payload مع معلومات الحماية
      final payload = {
        'url': url,
        'videoId': videoId,
        'deviceId': deviceId,
        'timestamp': timestamp,
        'deviceKey': deviceKey,
      };

      final jsonPayload = json.encode(payload);
      final encryptedPayload = encryptText(jsonPayload);
      final hash = generateHash(jsonPayload);

      return {
        'encryptedData': encryptedPayload,
        'hash': hash,
        'timestamp': timestamp,
      };
    } catch (e) {
      throw Exception('فشل في تشفير رابط الفيديو: $e');
    }
  }

  /// فك تشفير رابط الفيديو مع التحقق من الصحة
  Map<String, dynamic>? decryptVideoUrl(
    Map<String, String> encryptedData,
    String deviceId,
  ) {
    try {
      final encryptedPayload = encryptedData['encryptedData'];
      final expectedHash = encryptedData['hash'];
      final timestamp = encryptedData['timestamp'];

      if (encryptedPayload == null ||
          expectedHash == null ||
          timestamp == null) {
        return null;
      }

      // فك التشفير
      final jsonPayload = decryptText(encryptedPayload);

      // التحقق من hash
      if (!verifyHash(jsonPayload, expectedHash)) {
        return null;
      }

      final payload = json.decode(jsonPayload) as Map<String, dynamic>;

      // التحقق من معرف الجهاز
      if (payload['deviceId'] != deviceId) {
        return null;
      }

      // التحقق من مفتاح الجهاز
      final expectedDeviceKey = generateDeviceKey(deviceId);
      if (payload['deviceKey'] != expectedDeviceKey) {
        return null;
      }

      // التحقق من انتهاء الصلاحية (24 ساعة)
      final payloadTimestamp = int.parse(payload['timestamp']);
      final currentTimestamp = DateTime.now().millisecondsSinceEpoch;
      const validityPeriod = 24 * 60 * 60 * 1000; // 24 ساعة بالميلي ثانية

      if (currentTimestamp - payloadTimestamp > validityPeriod) {
        return null; // انتهت الصلاحية
      }

      return payload;
    } catch (e) {
      return null;
    }
  }

  /// إنشاء اسم ملف مشفر للفيديو المحمل
  String generateEncryptedFileName(String videoId, String deviceId) {
    final combined =
        '$videoId$deviceId${DateTime.now().millisecondsSinceEpoch}';
    final hash = generateHash(combined);
    return '${hash.substring(0, 16)}.enc'; // ملف مشفر بامتداد .enc
  }
}
