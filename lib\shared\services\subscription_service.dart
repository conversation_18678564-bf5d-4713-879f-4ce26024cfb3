import 'package:flutter/material.dart';
import '../models/subject_model.dart';
import '../models/user_subscription_model.dart';
import '../models/video_subject_model.dart';
import 'firebase_service.dart';
import 'device_service.dart';
import 'offline_storage_service.dart';
import 'video_service.dart';

class SubscriptionService extends ChangeNotifier {
  static SubscriptionService? _instance;
  static SubscriptionService get instance =>
      _instance ??= SubscriptionService._();

  SubscriptionService._();

  final FirebaseService _firebaseService = FirebaseService.instance;
  final DeviceService _deviceService = DeviceService.instance;

  UserSubscription? _currentSubscription;
  List<Subject> _allSubjects = [];
  List<Subject> _subscribedSubjects = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  UserSubscription? get currentSubscription => _currentSubscription;
  List<Subject> get allSubjects => _allSubjects;
  List<Subject> get subscribedSubjects => _subscribedSubjects;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// الحصول على المواد المدفوعة فقط
  List<Subject> get paidSubjects {
    final paid = _allSubjects.where((subject) => !subject.isFree).toList();
    debugPrint(
      'المواد المدفوعة: ${paid.map((s) => '${s.name} (isFree: ${s.isFree})').join(', ')}',
    );
    return paid;
  }

  /// الحصول على المواد المجانية فقط
  List<Subject> get freeSubjects {
    final free = _allSubjects.where((subject) => subject.isFree).toList();
    debugPrint(
      'المواد المجانية: ${free.map((s) => '${s.name} (isFree: ${s.isFree})').join(', ')}',
    );
    return free;
  }

  /// تهيئة الخدمة
  Future<void> initialize() async {
    _setLoading(true);
    try {
      // تحميل البيانات المحلية أولاً
      await _loadOfflineData();

      // تسجيل دخول مجهول
      await _firebaseService.signInAnonymously();

      // تحميل جميع المواد
      await loadAllSubjects();

      // تحميل اشتراك المستخدم من الخادم وتحديث البيانات المحلية
      await loadUserSubscription();

      _clearError();
    } catch (e) {
      _setError('فشل في تهيئة الخدمة: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// تحميل البيانات المحلية
  Future<void> _loadOfflineData() async {
    try {
      // تحميل الاشتراك المحلي
      _currentSubscription = await OfflineStorageService.instance
          .getOfflineUserSubscription();

      // تحميل المواد المشترك بها محلياً
      _subscribedSubjects = await OfflineStorageService.instance
          .getOfflineSubscribedSubjects();

      // تحميل جميع المواد محلياً
      final offlineSubjects = await OfflineStorageService.instance
          .getOfflineSubjects();
      if (offlineSubjects.isNotEmpty) {
        _allSubjects = offlineSubjects;
      }

      notifyListeners();
    } catch (e) {
      // في حالة فشل تحميل البيانات المحلية، نتجاهل الخطأ ونحمل من الخادم
    }
  }

  /// تحميل جميع المواد
  Future<void> loadAllSubjects() async {
    try {
      _allSubjects = await _firebaseService.getAllSubjects();

      // حفظ المواد محلياً
      if (_allSubjects.isNotEmpty) {
        await OfflineStorageService.instance.saveSubjects(_allSubjects);
      }

      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل المواد: $e');
    }
  }

  /// تحميل اشتراك المستخدم
  Future<void> loadUserSubscription() async {
    try {
      _currentSubscription = await _firebaseService.getUserSubscription();
      await _loadSubscribedSubjects();

      // حفظ البيانات محلياً
      if (_currentSubscription != null) {
        await OfflineStorageService.instance.saveUserSubscription(
          _currentSubscription!,
        );
      }
      if (_subscribedSubjects.isNotEmpty) {
        await OfflineStorageService.instance.saveSubscribedSubjects(
          _subscribedSubjects,
        );
      }

      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل الاشتراك: $e');
    }
  }

  /// تحميل المواد المشترك بها
  Future<void> _loadSubscribedSubjects() async {
    if (_currentSubscription == null) {
      _subscribedSubjects = [];
      return;
    }

    try {
      _subscribedSubjects = await _firebaseService.getUserSubscribedSubjects();
    } catch (e) {
      _setError('فشل في تحميل المواد المشترك بها: $e');
    }
  }

  /// استخدام كود الاشتراك
  Future<SubscriptionResult> useSubscriptionCode(String code) async {
    if (code.trim().isEmpty) {
      return SubscriptionResult.error('يرجى إدخال الكود');
    }

    _setLoading(true);
    try {
      print('🔄 بدء تفعيل كود الاشتراك: $code');

      // التحقق من صحة الكود أولاً مع إعادة المحاولة
      final subscriptionCode = await _validateCodeWithRetry(code);
      if (subscriptionCode == null) {
        print('❌ الكود غير صحيح أو منتهي الصلاحية');
        return SubscriptionResult.error('الكود غير صحيح أو منتهي الصلاحية');
      }

      // التحقق من أن الكود لم يُستخدم
      if (subscriptionCode.isUsed) {
        print('❌ الكود مستخدم بالفعل');
        return SubscriptionResult.error('هذا الكود تم استخدامه من قبل');
      }

      // التحقق من أن المستخدم لم يستخدم هذا الكود من قبل
      if (_currentSubscription?.hasUsedCode(code) == true) {
        print('❌ المستخدم استخدم هذا الكود من قبل');
        return SubscriptionResult.error('لقد استخدمت هذا الكود من قبل');
      }

      print('✅ الكود صحيح، بدء التفعيل...');

      // استخدام الكود مباشرة بدون إعادة محاولة معقدة
      final success = await _firebaseService.useSubscriptionCode(code);
      if (!success) {
        print('❌ فشل في تفعيل الكود');
        return SubscriptionResult.error(
          'فشل في تفعيل الكود، يرجى المحاولة مرة أخرى',
        );
      }

      print('✅ تم تفعيل الكود بنجاح، إعادة تحميل البيانات...');

      // إعادة تحميل البيانات وحفظها محلياً
      await loadUserSubscription();

      // الحصول على أسماء المواد المفعلة
      String activatedMessage = 'تم تفعيل الاشتراك بنجاح!\n';

      // التحقق من مواد الاختبارات
      if (subscriptionCode.subjectIds.contains('all_subjects')) {
        activatedMessage += 'تم تفعيل جميع مواد الاختبارات المتاحة';
      } else if (subscriptionCode.subjectIds.isNotEmpty) {
        final activatedSubjects = _allSubjects
            .where(
              (subject) => subscriptionCode.subjectIds.contains(subject.id),
            )
            .map((subject) => subject.name)
            .toList();
        if (activatedSubjects.isNotEmpty) {
          activatedMessage +=
              'مواد الاختبارات المفعلة: ${activatedSubjects.join(', ')}';
        }
      }

      // التحقق من مواد الفيديوهات
      if (subscriptionCode.videoSubjectIds.contains('all_video_subjects')) {
        if (subscriptionCode.subjectIds.isNotEmpty) {
          activatedMessage += '\n';
        }
        activatedMessage += 'تم تفعيل جميع مواد الفيديوهات المتاحة';
      } else if (subscriptionCode.videoSubjectIds.isNotEmpty) {
        // تحميل مواد الفيديوهات للحصول على أسمائها
        final videoSubjects = await _getVideoSubjectNames(
          subscriptionCode.videoSubjectIds,
        );
        if (videoSubjects.isNotEmpty) {
          if (subscriptionCode.subjectIds.isNotEmpty) {
            activatedMessage += '\n';
          }
          activatedMessage +=
              'مواد الفيديوهات المفعلة: ${videoSubjects.join(', ')}';
        }
      }

      return SubscriptionResult.success(activatedMessage);
    } catch (e) {
      return SubscriptionResult.error('حدث خطأ: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// التحقق من صحة الكود مع إعادة المحاولة
  Future<dynamic> _validateCodeWithRetry(
    String code, {
    int maxRetries = 3,
  }) async {
    for (int i = 0; i < maxRetries; i++) {
      try {
        final result = await _firebaseService.validateCode(code);
        if (result != null) {
          return result;
        }
        // إذا كان الكود null، ننتظر قليلاً ونعيد المحاولة
        if (i < maxRetries - 1) {
          await Future.delayed(Duration(milliseconds: 500 * (i + 1)));
        }
      } catch (e) {
        if (i == maxRetries - 1) rethrow;
        await Future.delayed(Duration(milliseconds: 500 * (i + 1)));
      }
    }
    return null;
  }

  /// التحقق من اشتراك المستخدم في مادة معينة
  bool isSubscribedToSubject(String subjectId) {
    return _currentSubscription?.isSubscribedToSubject(subjectId) ?? false;
  }

  /// التحقق من وجود اشتراك نشط
  bool hasActiveSubscription() {
    return _currentSubscription != null &&
        _currentSubscription!.isActive &&
        _currentSubscription!.expiresAt.isAfter(DateTime.now());
  }

  /// الحصول على معرفات مواد الفيديوهات المسموحة
  List<String> getAllowedVideoSubjectIds() {
    if (_currentSubscription == null || !_currentSubscription!.isActive) {
      return [];
    }
    return _currentSubscription!.videoSubjectIds;
  }

  /// التحقق من اشتراك المستخدم في مادة فيديو معينة
  bool isSubscribedToVideoSubject(String videoSubjectId) {
    return _currentSubscription?.videoSubjectIds.contains(videoSubjectId) ??
        false;
  }

  /// الحصول على أسماء مواد الفيديوهات بناءً على معرفاتها
  Future<List<String>> _getVideoSubjectNames(
    List<String> videoSubjectIds,
  ) async {
    try {
      final videoService = VideoService.instance;
      final allVideoSubjects = await videoService.getAllVideoSubjects();

      return allVideoSubjects
          .where((subject) => videoSubjectIds.contains(subject.id))
          .map((subject) => subject.name)
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// الحصول على مواد الفيديوهات المفعلة
  Future<List<VideoSubject>> getSubscribedVideoSubjects() async {
    if (_currentSubscription == null ||
        _currentSubscription!.videoSubjectIds.isEmpty) {
      return [];
    }

    try {
      final videoService = VideoService.instance;
      final allVideoSubjects = await videoService.getAllVideoSubjects();

      return allVideoSubjects
          .where(
            (subject) =>
                _currentSubscription!.videoSubjectIds.contains(subject.id),
          )
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// الحصول على تاريخ تفعيل مادة معينة
  DateTime? getSubjectActivationDate(String subjectId) {
    return _currentSubscription?.getSubjectActivationDate(subjectId);
  }

  /// الحصول على عدد المواد المشترك بها
  int get subscribedSubjectsCount {
    return _currentSubscription?.subscribedSubjectIds.length ?? 0;
  }

  /// الحصول على عدد الأكواد المستخدمة
  int get usedCodesCount {
    return _currentSubscription?.usedCodes.length ?? 0;
  }

  /// الحصول على معلومات الجهاز
  Future<Map<String, String>> getDeviceInfo() async {
    return await _deviceService.getDeviceInfo();
  }

  /// إعادة تحميل البيانات
  Future<void> refresh() async {
    await initialize();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}

/// نتيجة عملية الاشتراك
class SubscriptionResult {
  final bool isSuccess;
  final String message;

  SubscriptionResult._(this.isSuccess, this.message);

  factory SubscriptionResult.success(String message) {
    return SubscriptionResult._(true, message);
  }

  factory SubscriptionResult.error(String message) {
    return SubscriptionResult._(false, message);
  }
}
