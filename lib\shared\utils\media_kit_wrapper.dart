import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';

/// Wrapper لـ media_kit للاستخدام على Windows
class MediaKitWrapper {
  static Player? _player;
  static VideoController? _videoController;
  static bool _isInitialized = false;

  /// تهيئة media_kit (يجب استدعاؤها مرة واحدة في main)
  static void ensureInitialized() {
    if (!_isInitialized) {
      MediaKit.ensureInitialized();
      _isInitialized = true;
      debugPrint('✅ MediaKit تم تهيئته بنجاح');
    }
  }

  /// إنشاء مشغل فيديو جديد
  static Future<VideoController> createVideoPlayer(String videoPath) async {
    try {
      debugPrint('🎬 إنشاء مشغل فيديو لـ: $videoPath');
      
      // إنشاء player جديد
      _player = Player();
      
      // إنشاء video controller
      _videoController = VideoController(_player!);
      
      // تحميل الفيديو
      await _player!.open(Media(videoPath));
      
      debugPrint('✅ تم إنشاء مشغل الفيديو بنجاح');
      return _videoController!;
      
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء مشغل الفيديو: $e');
      rethrow;
    }
  }

  /// تشغيل الفيديو
  static Future<void> play() async {
    if (_player != null) {
      await _player!.play();
      debugPrint('▶️ تم تشغيل الفيديو');
    }
  }

  /// إيقاف الفيديو
  static Future<void> pause() async {
    if (_player != null) {
      await _player!.pause();
      debugPrint('⏸️ تم إيقاف الفيديو');
    }
  }

  /// إيقاف وتنظيف الموارد
  static Future<void> dispose() async {
    if (_player != null) {
      await _player!.dispose();
      _player = null;
      _videoController = null;
      debugPrint('🗑️ تم تنظيف موارد مشغل الفيديو');
    }
  }

  /// الحصول على حالة التشغيل
  static bool get isPlaying {
    return _player?.state.playing ?? false;
  }

  /// الحصول على مدة الفيديو
  static Duration get duration {
    return _player?.state.duration ?? Duration.zero;
  }

  /// الحصول على الموضع الحالي
  static Duration get position {
    return _player?.state.position ?? Duration.zero;
  }

  /// تغيير الموضع
  static Future<void> seekTo(Duration position) async {
    if (_player != null) {
      await _player!.seek(position);
      debugPrint('⏭️ تم الانتقال إلى: ${position.inSeconds}s');
    }
  }

  /// تغيير مستوى الصوت (0.0 - 1.0)
  static Future<void> setVolume(double volume) async {
    if (_player != null) {
      await _player!.setVolume(volume * 100); // media_kit يستخدم 0-100
      debugPrint('🔊 تم تغيير مستوى الصوت إلى: ${(volume * 100).toInt()}%');
    }
  }

  /// الحصول على video controller للـ UI
  static VideoController? get videoController => _videoController;
  
  /// الحصول على player
  static Player? get player => _player;
}
