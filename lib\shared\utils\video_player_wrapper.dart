import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:video_player/video_player.dart';
import 'package:media_kit_video/media_kit_video.dart' as media_kit;
import 'media_kit_wrapper.dart';

/// Wrapper للتعامل مع video player على منصات مختلفة
class VideoPlayerWrapper {
  /// إنشاء video player controller بناءً على المنصة
  static VideoPlayerController createController({
    required String? videoPath,
    required String? networkUrl,
    bool isLocalFile = false,
  }) {
    try {
      // استخدام video_player العادي لجميع المنصات
      // media_kit سيتم استخدامه في صفحة منفصلة للـ Windows
      if (isLocalFile && videoPath != null) {
        // تشغيل ملف محلي
        debugPrint('🎬 إنشاء مشغل للملف المحلي: $videoPath');
        return VideoPlayerController.file(File(videoPath));
      } else if (networkUrl != null) {
        // تشغيل من الشبكة
        debugPrint('🌐 إنشاء مشغل للرابط: $networkUrl');
        return VideoPlayerController.networkUrl(Uri.parse(networkUrl));
      } else {
        throw Exception('لا يوجد مصدر فيديو صالح');
      }
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء مشغل الفيديو: $e');
      rethrow;
    }
  }

  /// التحقق من دعم تشغيل الفيديو على المنصة الحالية
  static bool isVideoPlaybackSupported() {
    if (kIsWeb) return true;

    try {
      if (Platform.isAndroid || Platform.isIOS) {
        return true;
      } else if (Platform.isWindows) {
        // video_player_win 3.2.0 مدعوم بالكامل
        debugPrint('🔍 Windows detected - video_player_win 3.2.0 مدعوم');
        return true;
      } else if (Platform.isMacOS || Platform.isLinux) {
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في التحقق من دعم تشغيل الفيديو: $e');
      return false;
    }
  }

  /// الحصول على رسالة خطأ مناسبة للمنصة
  static String getUnsupportedPlatformMessage() {
    if (kIsWeb) {
      return 'تشغيل الفيديو غير مدعوم في المتصفح حالياً';
    }

    try {
      if (Platform.isWindows) {
        return 'تم تحديث دعم Windows - يرجى إعادة تشغيل التطبيق';
      } else {
        return 'تشغيل الفيديو غير مدعوم على هذه المنصة';
      }
    } catch (e) {
      return 'خطأ في تحديد نوع المنصة';
    }
  }

  /// معلومات المنصة للتشخيص
  static String getPlatformInfo() {
    if (kIsWeb) return 'Web Platform';

    try {
      return 'Platform: ${Platform.operatingSystem} ${Platform.operatingSystemVersion}';
    } catch (e) {
      return 'Unknown Platform';
    }
  }
}
