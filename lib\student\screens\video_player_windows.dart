import 'dart:io';
import 'package:flutter/material.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import '../../shared/utils/media_kit_wrapper.dart';

/// صفحة تشغيل الفيديو للـ Windows باستخدام media_kit
class VideoPlayerWindows extends StatefulWidget {
  final String videoPath;
  final String videoTitle;

  const VideoPlayerWindows({
    super.key,
    required this.videoPath,
    required this.videoTitle,
  });

  @override
  State<VideoPlayerWindows> createState() => _VideoPlayerWindowsState();
}

class _VideoPlayerWindowsState extends State<VideoPlayerWindows> {
  late final Player _player;
  late final VideoController _videoController;
  bool _isInitialized = false;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      debugPrint('🎬 تهيئة مشغل الفيديو للـ Windows: ${widget.videoPath}');

      // إنشاء player
      _player = Player();
      _videoController = VideoController(_player);

      // تحميل الفيديو
      await _player.open(Media(widget.videoPath));

      setState(() {
        _isInitialized = true;
        _isLoading = false;
      });

      debugPrint('✅ تم تهيئة مشغل الفيديو بنجاح');

    } catch (e) {
      debugPrint('❌ خطأ في تهيئة مشغل الفيديو: $e');
      setState(() {
        _error = 'خطأ في تحميل الفيديو: $e';
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _player.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text(
          widget.videoTitle,
          style: const TextStyle(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.white),
            SizedBox(height: 16),
            Text(
              'جاري تحميل الفيديو...',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(color: Colors.white, fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initializePlayer,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (!_isInitialized) {
      return const Center(
        child: Text(
          'فشل في تهيئة مشغل الفيديو',
          style: TextStyle(color: Colors.white, fontSize: 16),
        ),
      );
    }

    return Center(
      child: AspectRatio(
        aspectRatio: 16 / 9, // نسبة عرض افتراضية
        child: Video(
          controller: _videoController,
          controls: AdaptiveVideoControls, // أدوات تحكم تلقائية
        ),
      ),
    );
  }
}
