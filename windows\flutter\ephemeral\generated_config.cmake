# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\1111\\flutter_windows_3.29.3-stable\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\1111\\flutter_windows_3.29.3-stable\\ss\\fff\\44\\vv\\price_ss\\smart_test" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\1111\\flutter_windows_3.29.3-stable\\flutter"
  "PROJECT_DIR=D:\\1111\\flutter_windows_3.29.3-stable\\ss\\fff\\44\\vv\\price_ss\\smart_test"
  "FLUTTER_ROOT=D:\\1111\\flutter_windows_3.29.3-stable\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\1111\\flutter_windows_3.29.3-stable\\ss\\fff\\44\\vv\\price_ss\\smart_test\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\1111\\flutter_windows_3.29.3-stable\\ss\\fff\\44\\vv\\price_ss\\smart_test"
  "FLUTTER_TARGET=D:\\1111\\flutter_windows_3.29.3-stable\\ss\\fff\\44\\vv\\price_ss\\smart_test\\lib\\main.dart"
  "DART_DEFINES=RkxBVk9SPXN0dWRlbnQ=,RkxVVFRFUl9BUFBfRkxBVk9SPXN0dWRlbnQ=,RkxVVFRFUl9WRVJTSU9OPTMuMzIuNA==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049NmZiYTI0NDdlOQ==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049OGNkMTllNTA5ZA==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\1111\\flutter_windows_3.29.3-stable\\ss\\fff\\44\\vv\\price_ss\\smart_test\\.dart_tool\\package_config.json"
  "FLAVOR=student"
)
